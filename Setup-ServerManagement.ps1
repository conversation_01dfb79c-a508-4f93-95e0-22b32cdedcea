#Requires -RunAsAdministrator
<#
.SYNOPSIS
    Setup-Skript für Windows Server Management v2.0
.DESCRIPTION
    Installiert und konfiguriert alle erforderlichen Komponenten für das
    moderne Windows Server Management System.
.AUTHOR
    Tonino Gerns
.VERSION
    2.0.0
#>

[CmdletBinding()]
param(
    [switch]$SkipPowerShellCheck,
    [switch]$ConfigureWinRM,
    [string]$InstallPath = $PSScriptRoot
)

# Farben für bessere Ausgabe
$Colors = @{
    Success = 'Green'
    Warning = 'Yellow'
    Error = 'Red'
    Info = 'Cyan'
    Header = 'Magenta'
}

function Write-SetupMessage {
    param(
        [string]$Message,
        [ValidateSet('Success', 'Warning', 'Error', 'Info', 'Header')]
        [string]$Type = 'Info'
    )
    
    $prefix = switch ($Type) {
        'Success' { '✅' }
        'Warning' { '⚠️ ' }
        'Error' { '❌' }
        'Info' { 'ℹ️ ' }
        'Header' { '🚀' }
    }
    
    Write-Host "$prefix $Message" -ForegroundColor $Colors[$Type]
}

function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Test-PowerShellVersion {
    $requiredVersion = [Version]'7.0'
    $currentVersion = $PSVersionTable.PSVersion
    
    Write-SetupMessage "Aktuelle PowerShell Version: $currentVersion" -Type Info
    
    if ($currentVersion -ge $requiredVersion) {
        Write-SetupMessage "PowerShell Version ist kompatibel" -Type Success
        return $true
    } else {
        Write-SetupMessage "PowerShell 7.0+ erforderlich. Aktuelle Version: $currentVersion" -Type Error
        return $false
    }
}

function Install-PowerShell7 {
    Write-SetupMessage "Installiere PowerShell 7..." -Type Info
    
    try {
        # Prüfe ob winget verfügbar ist
        $winget = Get-Command winget -ErrorAction SilentlyContinue
        if ($winget) {
            Write-SetupMessage "Verwende winget für Installation..." -Type Info
            winget install Microsoft.PowerShell --accept-source-agreements --accept-package-agreements
        } else {
            # Fallback: Direkte Installation
            Write-SetupMessage "Lade PowerShell 7 direkt herunter..." -Type Info
            $downloadUrl = "https://github.com/PowerShell/PowerShell/releases/latest/download/PowerShell-7.4.0-win-x64.msi"
            $tempFile = Join-Path $env:TEMP "PowerShell-7-x64.msi"
            
            Invoke-WebRequest -Uri $downloadUrl -OutFile $tempFile
            Start-Process msiexec.exe -ArgumentList "/i `"$tempFile`" /quiet" -Wait
            Remove-Item $tempFile -Force
        }
        
        Write-SetupMessage "PowerShell 7 erfolgreich installiert" -Type Success
        Write-SetupMessage "WICHTIG: Starten Sie das Skript in PowerShell 7 neu!" -Type Warning
        return $true
    }
    catch {
        Write-SetupMessage "Fehler bei PowerShell 7 Installation: $_" -Type Error
        return $false
    }
}

function Test-WinRMConfiguration {
    Write-SetupMessage "Prüfe WinRM Konfiguration..." -Type Info
    
    try {
        $winrmService = Get-Service WinRM -ErrorAction Stop
        if ($winrmService.Status -eq 'Running') {
            Write-SetupMessage "WinRM Service läuft" -Type Success
            
            # Prüfe WinRM Konfiguration
            $winrmConfig = winrm get winrm/config/client
            if ($winrmConfig) {
                Write-SetupMessage "WinRM ist konfiguriert" -Type Success
                return $true
            }
        } else {
            Write-SetupMessage "WinRM Service ist gestoppt" -Type Warning
            return $false
        }
    }
    catch {
        Write-SetupMessage "WinRM ist nicht konfiguriert" -Type Warning
        return $false
    }
}

function Configure-WinRM {
    Write-SetupMessage "Konfiguriere WinRM..." -Type Info
    
    try {
        # WinRM aktivieren
        Enable-PSRemoting -Force -SkipNetworkProfileCheck
        
        # Vertrauenswürdige Hosts konfigurieren (für Arbeitsgruppen-Umgebung)
        $trustHosts = Read-Host "Vertrauenswürdige Hosts eingeben (z.B. '*.domain.local' oder '*' für alle) [Enter für Standard]"
        if ([string]::IsNullOrEmpty($trustHosts)) {
            $trustHosts = "*.local"
        }
        
        Set-Item WSMan:\localhost\Client\TrustedHosts -Value $trustHosts -Force
        
        # Firewall-Regeln aktivieren
        Enable-NetFirewallRule -DisplayGroup "Windows Remote Management"
        
        # WinRM Service starten
        Start-Service WinRM
        Set-Service WinRM -StartupType Automatic
        
        Write-SetupMessage "WinRM erfolgreich konfiguriert" -Type Success
        Write-SetupMessage "Vertrauenswürdige Hosts: $trustHosts" -Type Info
        return $true
    }
    catch {
        Write-SetupMessage "Fehler bei WinRM Konfiguration: $_" -Type Error
        return $false
    }
}

function Test-RequiredFiles {
    Write-SetupMessage "Prüfe erforderliche Dateien..." -Type Info
    
    $requiredFiles = @(
        'windows_server_verwaltung_modern.ps1',
        'ServerManagement.psm1',
        'ServerConfig.psd1'
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        $filePath = Join-Path $InstallPath $file
        if (-not (Test-Path $filePath)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -eq 0) {
        Write-SetupMessage "Alle erforderlichen Dateien gefunden" -Type Success
        return $true
    } else {
        Write-SetupMessage "Fehlende Dateien: $($missingFiles -join ', ')" -Type Error
        return $false
    }
}

function Initialize-Configuration {
    Write-SetupMessage "Initialisiere Konfiguration..." -Type Info
    
    $configPath = Join-Path $InstallPath "ServerConfig.psd1"
    
    if (Test-Path $configPath) {
        try {
            $config = Import-PowerShellDataFile -Path $configPath
            Write-SetupMessage "Konfiguration erfolgreich geladen" -Type Success
            
            # Zeige Server-Liste
            Write-SetupMessage "Konfigurierte Server:" -Type Info
            foreach ($server in $config.ServerList) {
                Write-Host "  - $($server.Name) ($($server.Role))" -ForegroundColor White
            }
            
            # Frage nach Anpassungen
            $customize = Read-Host "`nMöchten Sie die Server-Konfiguration anpassen? (j/n)"
            if ($customize -eq 'j') {
                Write-SetupMessage "Öffne Konfigurationsdatei zum Bearbeiten..." -Type Info
                notepad.exe $configPath
                Write-Host "Drücken Sie Enter wenn Sie fertig sind..." -ForegroundColor Yellow
                Read-Host
            }
            
            return $true
        }
        catch {
            Write-SetupMessage "Fehler beim Laden der Konfiguration: $_" -Type Error
            return $false
        }
    } else {
        Write-SetupMessage "Konfigurationsdatei nicht gefunden: $configPath" -Type Error
        return $false
    }
}

function Create-Shortcuts {
    Write-SetupMessage "Erstelle Desktop-Verknüpfungen..." -Type Info
    
    try {
        $shell = New-Object -ComObject WScript.Shell
        $desktopPath = [Environment]::GetFolderPath("Desktop")
        
        # Hauptskript Verknüpfung
        $shortcutPath = Join-Path $desktopPath "Server Management v2.0.lnk"
        $shortcut = $shell.CreateShortcut($shortcutPath)
        $shortcut.TargetPath = "pwsh.exe"
        $shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$(Join-Path $InstallPath 'windows_server_verwaltung_modern.ps1')`""
        $shortcut.WorkingDirectory = $InstallPath
        $shortcut.Description = "Windows Server Management v2.0"
        $shortcut.IconLocation = "shell32.dll,21"
        $shortcut.Save()
        
        Write-SetupMessage "Desktop-Verknüpfung erstellt" -Type Success
        return $true
    }
    catch {
        Write-SetupMessage "Fehler beim Erstellen der Verknüpfungen: $_" -Type Warning
        return $false
    }
}

function Test-ServerConnectivity {
    Write-SetupMessage "Teste Server-Konnektivität..." -Type Info
    
    try {
        $configPath = Join-Path $InstallPath "ServerConfig.psd1"
        $config = Import-PowerShellDataFile -Path $configPath
        
        $onlineServers = 0
        $totalServers = $config.ServerList.Count
        
        foreach ($server in $config.ServerList) {
            try {
                $ping = Test-Connection -ComputerName $server.Name -Count 1 -TimeoutSeconds 3 -ErrorAction Stop
                Write-Host "  ✅ $($server.Name) - Online ($('{0}ms' -f $ping.ResponseTime))" -ForegroundColor Green
                $onlineServers++
            }
            catch {
                Write-Host "  ❌ $($server.Name) - Offline" -ForegroundColor Red
            }
        }
        
        Write-SetupMessage "Server-Konnektivität: $onlineServers/$totalServers Server online" -Type Info
        
        if ($onlineServers -eq 0) {
            Write-SetupMessage "WARNUNG: Keine Server erreichbar. Prüfen Sie Netzwerk und Servernamen." -Type Warning
        }
        
        return $true
    }
    catch {
        Write-SetupMessage "Fehler beim Testen der Server-Konnektivität: $_" -Type Error
        return $false
    }
}

# Hauptsetup-Funktion
function Start-Setup {
    Clear-Host
    
    Write-Host @"
🚀 Windows Server Management v2.0 Setup
========================================

Dieses Setup installiert und konfiguriert alle erforderlichen Komponenten
für das moderne Windows Server Management System.

"@ -ForegroundColor $Colors.Header
    
    # 1. Administratorrechte prüfen
    if (-not (Test-AdminRights)) {
        Write-SetupMessage "Dieses Setup muss als Administrator ausgeführt werden!" -Type Error
        exit 1
    }
    Write-SetupMessage "Administratorrechte bestätigt" -Type Success
    
    # 2. PowerShell Version prüfen
    if (-not $SkipPowerShellCheck) {
        if (-not (Test-PowerShellVersion)) {
            $install = Read-Host "PowerShell 7 installieren? (j/n)"
            if ($install -eq 'j') {
                if (Install-PowerShell7) {
                    Write-SetupMessage "Bitte starten Sie das Setup in PowerShell 7 neu!" -Type Warning
                    exit 0
                } else {
                    exit 1
                }
            } else {
                Write-SetupMessage "Setup abgebrochen - PowerShell 7 erforderlich" -Type Error
                exit 1
            }
        }
    }
    
    # 3. Erforderliche Dateien prüfen
    if (-not (Test-RequiredFiles)) {
        Write-SetupMessage "Setup abgebrochen - Dateien fehlen" -Type Error
        exit 1
    }
    
    # 4. WinRM konfigurieren
    if ($ConfigureWinRM -or -not (Test-WinRMConfiguration)) {
        $configWinRM = Read-Host "WinRM konfigurieren? (j/n)"
        if ($configWinRM -eq 'j') {
            Configure-WinRM
        }
    }
    
    # 5. Konfiguration initialisieren
    if (-not (Initialize-Configuration)) {
        Write-SetupMessage "Setup abgebrochen - Konfigurationsfehler" -Type Error
        exit 1
    }
    
    # 6. Desktop-Verknüpfungen erstellen
    $createShortcuts = Read-Host "Desktop-Verknüpfungen erstellen? (j/n)"
    if ($createShortcuts -eq 'j') {
        Create-Shortcuts
    }
    
    # 7. Server-Konnektivität testen
    $testConnectivity = Read-Host "Server-Konnektivität testen? (j/n)"
    if ($testConnectivity -eq 'j') {
        Test-ServerConnectivity
    }
    
    # Setup abgeschlossen
    Write-Host "`n" -NoNewline
    Write-SetupMessage "Setup erfolgreich abgeschlossen!" -Type Success
    Write-Host @"

📋 Nächste Schritte:
1. Starten Sie das Server Management über die Desktop-Verknüpfung
2. Oder führen Sie direkt aus: pwsh.exe -File "$(Join-Path $InstallPath 'windows_server_verwaltung_modern.ps1')"
3. Passen Sie bei Bedarf die ServerConfig.psd1 an Ihre Umgebung an

📚 Dokumentation: README.md
🔧 Erweiterte Features: ExtendedFeatures.ps1

"@ -ForegroundColor White
    
    $startNow = Read-Host "Server Management jetzt starten? (j/n)"
    if ($startNow -eq 'j') {
        & pwsh.exe -ExecutionPolicy Bypass -File (Join-Path $InstallPath 'windows_server_verwaltung_modern.ps1')
    }
}

# Setup starten
Start-Setup
