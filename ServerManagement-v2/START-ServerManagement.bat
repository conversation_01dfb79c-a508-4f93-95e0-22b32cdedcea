@echo off
title Windows Server Management v2.0
echo.
echo ========================================
echo  Windows Server Management v2.0
echo ========================================
echo.
echo Starte PowerShell 7 mit Server Management...
echo.

REM Prüfe ob PowerShell 7 verfügbar ist
where pwsh >nul 2>nul
if %errorlevel% neq 0 (
    echo FEHLER: PowerShell 7 ist nicht installiert!
    echo Bitte installieren Sie PowerShell 7 zuerst:
    echo winget install Microsoft.PowerShell
    echo.
    pause
    exit /b 1
)

REM Starte das PowerShell Skript
pwsh.exe -ExecutionPolicy Bypass -File "%~dp0ServerManagement.ps1"

REM Warte auf Benutzereingabe falls Fehler aufgetreten ist
if %errorlevel% neq 0 (
    echo.
    echo Ein Fehler ist aufgetreten. Drücken Sie eine Taste zum Beenden...
    pause >nul
)
