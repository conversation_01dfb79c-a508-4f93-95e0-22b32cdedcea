using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;

namespace SimpleServerGUI
{
    public partial class MainWindow : Window
    {
        private readonly DispatcherTimer _timer;
        private readonly ObservableCollection<ServerItem> _servers;

        public MainWindow()
        {
            InitializeComponent();
            
            _servers = new ObservableCollection<ServerItem>();
            ServerList.ItemsSource = _servers;
            
            // Initialize servers
            InitializeServers();
            
            // Timer for status bar
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();
            
            AddLogEntry("Windows Server Management v2.0 gestartet");
            AddLogEntry("GUI-Version erfolgreich geladen");
        }

        private void InitializeServers()
        {
            var servers = new[]
            {
                new ServerItem { Name = "DC01", Role = "Domain Controller", Icon = "❓", Status = "Unbekannt" },
                new ServerItem { Name = "DC02", Role = "Domain Controller", Icon = "❓", Status = "Unbekannt" },
                new ServerItem { Name = "DB01", Role = "Database Server", Icon = "❓", Status = "Unbekannt" },
                new ServerItem { Name = "APP01", Role = "Application Server", Icon = "❓", Status = "Unbekannt" },
                new ServerItem { Name = "MAIL", Role = "Mail Server", Icon = "❓", Status = "Unbekannt" },
                new ServerItem { Name = "FILE", Role = "File Server", Icon = "❓", Status = "Unbekannt" },
                new ServerItem { Name = "TS01", Role = "Terminal Server", Icon = "❓", Status = "Unbekannt" }
            };

            foreach (var server in servers)
            {
                _servers.Add(server);
            }
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            TimeText.Text = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
        }

        private void AddLogEntry(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            LogTextBox.AppendText($"\n[{timestamp}] {message}");
            LogTextBox.ScrollToEnd();
        }

        private async void PingButton_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "Teste Server-Verbindungen...";
            AddLogEntry("Starte Ping-Test für alle Server");

            foreach (var server in _servers)
            {
                try
                {
                    using (var ping = new Ping())
                    {
                        var reply = await ping.SendPingAsync(server.Name, 3000);
                        
                        if (reply.Status == IPStatus.Success)
                        {
                            server.Icon = "✅";
                            server.Status = $"Online ({reply.RoundtripTime}ms)";
                            AddLogEntry($"Server {server.Name}: Online ({reply.RoundtripTime}ms)");
                        }
                        else
                        {
                            server.Icon = "❌";
                            server.Status = "Offline";
                            AddLogEntry($"Server {server.Name}: Offline ({reply.Status})");
                        }
                    }
                }
                catch (Exception ex)
                {
                    server.Icon = "❌";
                    server.Status = "Fehler";
                    AddLogEntry($"Server {server.Name}: Fehler - {ex.Message}");
                }
            }

            var onlineCount = 0;
            foreach (var server in _servers)
            {
                if (server.Icon == "✅") onlineCount++;
            }

            StatusText.Text = $"{onlineCount}/{_servers.Count} Server online";
            AddLogEntry($"Ping-Test abgeschlossen: {onlineCount}/{_servers.Count} Server online");
        }

        private void UpdatesButton_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "Suche Windows Updates...";
            AddLogEntry("Windows Update-Suche gestartet");
            AddLogEntry("HINWEIS: Diese Funktion benötigt PowerShell-Integration");
            AddLogEntry("Verwenden Sie das PowerShell-Skript für vollständige Funktionalität");
            StatusText.Text = "Update-Suche - PowerShell-Integration erforderlich";
        }

        private void RestartButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "Möchten Sie alle Server in der korrekten Reihenfolge neu starten?\n\n" +
                "Reihenfolge: DC01 → DC02 → DB01 → APP01 → MAIL → FILE → TS01\n\n" +
                "HINWEIS: Diese Funktion benötigt PowerShell-Integration für die tatsächliche Ausführung.",
                "Server neu starten", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                StatusText.Text = "Server-Neustart - PowerShell-Integration erforderlich";
                AddLogEntry("Server-Neustart angefordert");
                AddLogEntry("HINWEIS: Verwenden Sie das PowerShell-Skript für tatsächliche Neustarts");
            }
        }

        private void StatusButton_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "Zeige Server-Status...";
            AddLogEntry("Server-Status angefordert");
            
            var statusInfo = $"SYSTEM-STATUS\n" +
                           $"═════════════\n\n" +
                           $"🖥️  Computer: {Environment.MachineName}\n" +
                           $"👤 Benutzer: {Environment.UserName}\n" +
                           $"🌐 Domain: {Environment.UserDomainName}\n" +
                           $"⚙️  .NET Version: {Environment.Version}\n" +
                           $"🕒 Zeit: {DateTime.Now:dd.MM.yyyy HH:mm:ss}\n\n" +
                           $"✅ GUI-Version läuft erfolgreich!\n" +
                           $"📋 Für vollständige Server-Details verwenden Sie das PowerShell-Skript";

            MessageBox.Show(statusInfo, "System-Status", MessageBoxButton.OK, MessageBoxImage.Information);
            
            AddLogEntry("System-Status angezeigt");
            StatusText.Text = "System-Status angezeigt";
        }
    }

    public class ServerItem : INotifyPropertyChanged
    {
        private string _icon;
        private string _status;

        public string Name { get; set; }
        public string Role { get; set; }
        
        public string Icon
        {
            get => _icon;
            set
            {
                _icon = value;
                OnPropertyChanged(nameof(Icon));
            }
        }
        
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
