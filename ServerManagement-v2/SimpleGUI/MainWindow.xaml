<Window x:Class="SimpleServerGUI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Windows Server Management v2.0" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen" Background="#FAFAFA">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="16">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🖥️" FontSize="24" VerticalAlignment="Center" Margin="0,0,12,0"/>
                <TextBlock Text="Windows Server Management v2.0" 
                           FontSize="20" FontWeight="Bold" 
                           VerticalAlignment="Center" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <Border Grid.Column="0" Background="White" BorderBrush="#E0E0E0" 
                    BorderThickness="1" CornerRadius="8" Padding="16" Margin="0,0,16,0">
                <StackPanel>
                    <TextBlock Text="AKTIONEN" FontWeight="Bold" Margin="0,0,0,16" 
                               Foreground="#757575" FontSize="12"/>
                    
                    <Button x:Name="PingButton" Style="{StaticResource ActionButton}" 
                            Click="PingButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📡" Margin="0,0,8,0"/>
                            <TextBlock Text="Server anpingen"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="UpdatesButton" Style="{StaticResource ActionButton}" 
                            Click="UpdatesButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔍" Margin="0,0,8,0"/>
                            <TextBlock Text="Updates suchen"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="RestartButton" Style="{StaticResource ActionButton}" 
                            Click="RestartButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" Margin="0,0,8,0"/>
                            <TextBlock Text="Server neu starten"/>
                        </StackPanel>
                    </Button>

                    <Button x:Name="StatusButton" Style="{StaticResource ActionButton}" 
                            Click="StatusButton_Click">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="📊" Margin="0,0,8,0"/>
                            <TextBlock Text="Server-Status"/>
                        </StackPanel>
                    </Button>

                    <Rectangle Height="1" Fill="#E0E0E0" Margin="0,16"/>

                    <TextBlock Text="SERVER" FontWeight="Bold" Margin="0,0,0,16" 
                               Foreground="#757575" FontSize="12"/>
                    
                    <CheckBox x:Name="SelectAllCheckBox" Content="Alle Server" 
                              Margin="0,0,0,8" IsChecked="True"/>
                    
                    <CheckBox Content="DC01" Margin="0,0,0,4" IsChecked="True"/>
                    <CheckBox Content="DC02" Margin="0,0,0,4" IsChecked="True"/>
                    <CheckBox Content="DB01" Margin="0,0,0,4" IsChecked="True"/>
                    <CheckBox Content="APP01" Margin="0,0,0,4" IsChecked="True"/>
                    <CheckBox Content="MAIL" Margin="0,0,0,4" IsChecked="True"/>
                    <CheckBox Content="FILE" Margin="0,0,0,4" IsChecked="True"/>
                    <CheckBox Content="TS01" Margin="0,0,0,4" IsChecked="True"/>
                </StackPanel>
            </Border>

            <!-- Main Area -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Server Overview -->
                <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" 
                        BorderThickness="1" CornerRadius="8" Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <TextBlock Text="SERVER ÜBERSICHT" FontWeight="Bold" Margin="0,0,0,16"/>
                        
                        <ItemsControl x:Name="ServerList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#F5F5F5" CornerRadius="4" Padding="12" Margin="0,0,0,8">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="{Binding Icon}" 
                                                       FontSize="20" VerticalAlignment="Center" Margin="0,0,12,0"/>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" FontWeight="SemiBold"/>
                                                <TextBlock Text="{Binding Status}" FontSize="12" Foreground="#757575"/>
                                            </StackPanel>
                                            
                                            <TextBlock Grid.Column="2" Text="{Binding Role}" 
                                                       FontSize="12" Foreground="#757575" VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Activity Log -->
                <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" 
                        BorderThickness="1" CornerRadius="8" Padding="16">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Text="AKTIVITÄTSPROTOKOLL" FontWeight="Bold" Margin="0,0,0,16"/>
                        
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <TextBox x:Name="LogTextBox" 
                                     IsReadOnly="True" 
                                     TextWrapping="Wrap"
                                     FontFamily="Consolas"
                                     FontSize="11"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Text="Server Management gestartet&#x0A;7 Server konfiguriert&#x0A;Bereit für Aktionen"/>
                        </ScrollViewer>
                    </Grid>
                </Border>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#37474F" Padding="12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock x:Name="StatusText" Grid.Column="0" 
                           Text="Bereit - 7 Server konfiguriert" 
                           Foreground="White" VerticalAlignment="Center"/>
                
                <TextBlock x:Name="TimeText" Grid.Column="1" 
                           Foreground="White" VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
