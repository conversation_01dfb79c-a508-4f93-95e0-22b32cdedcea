<Window x:Class="TestApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Test Server Management" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="16">
            <TextBlock Text="🖥️ Test Server Management" 
                       FontSize="20" FontWeight="Bold" 
                       Foreground="White" HorizontalAlignment="Center"/>
        </Border>

        <!-- Content -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <StackPanel Grid.Column="0" Margin="0,0,16,0">
                <TextBlock Text="AKTIONEN" FontWeight="Bold" Margin="0,0,0,16"/>
                
                <Button x:Name="TestButton" Content="Test Funktion" 
                        Padding="8" Margin="0,0,0,8" Click="TestButton_Click"/>
                
                <Button x:Name="PingButton" Content="Server anpingen" 
                        Padding="8" Margin="0,0,0,8" Click="PingButton_Click"/>
                
                <Button x:Name="StatusButton" Content="Status anzeigen" 
                        Padding="8" Margin="0,0,0,8" Click="StatusButton_Click"/>
            </StackPanel>

            <!-- Main Area -->
            <Border Grid.Column="1" BorderBrush="#E0E0E0" BorderThickness="1" Padding="16">
                <ScrollViewer>
                    <TextBlock x:Name="OutputText" 
                               Text="Willkommen beim Test Server Management!&#x0A;&#x0A;Klicken Sie auf einen Button um zu testen."
                               TextWrapping="Wrap" FontFamily="Consolas"/>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#37474F" Padding="8">
            <TextBlock x:Name="StatusText" Text="Bereit" Foreground="White"/>
        </Border>
    </Grid>
</Window>
