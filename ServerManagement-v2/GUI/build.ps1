#Requires -Version 7.0
<#
.SYNOPSIS
    Build-Skript für die Windows Server Management GUI-Anwendung
.DESCRIPTION
    Kompiliert die WPF-Anwendung und erstellt eine ausführbare Datei
#>

param(
    [ValidateSet('Debug', 'Release')]
    [string]$Configuration = 'Release',
    
    [switch]$Clean,
    
    [switch]$Publish
)

$ErrorActionPreference = 'Stop'

# Pfade
$ProjectPath = $PSScriptRoot
$ProjectFile = Join-Path $ProjectPath "ServerManagementApp.csproj"
$OutputPath = Join-Path $ProjectPath "bin\$Configuration"
$PublishPath = Join-Path $ProjectPath "publish"

Write-Host "🚀 Windows Server Management GUI Build" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

# Prüfe .NET 8 Installation
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Error ".NET 8 SDK ist nicht installiert. Bitte installieren Sie .NET 8 SDK von https://dotnet.microsoft.com/download"
    exit 1
}

# Clean Build
if ($Clean) {
    Write-Host "🧹 Bereinige Build-Verzeichnisse..." -ForegroundColor Yellow
    
    if (Test-Path $OutputPath) {
        Remove-Item $OutputPath -Recurse -Force
        Write-Host "  ✓ $OutputPath gelöscht" -ForegroundColor Gray
    }
    
    if (Test-Path $PublishPath) {
        Remove-Item $PublishPath -Recurse -Force
        Write-Host "  ✓ $PublishPath gelöscht" -ForegroundColor Gray
    }
    
    dotnet clean $ProjectFile --configuration $Configuration --verbosity quiet
    Write-Host "  ✓ Projekt bereinigt" -ForegroundColor Gray
    Write-Host ""
}

# Restore NuGet Packages
Write-Host "📦 Lade NuGet-Pakete..." -ForegroundColor Yellow
dotnet restore $ProjectFile --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Error "Fehler beim Laden der NuGet-Pakete"
    exit 1
}
Write-Host "  ✓ NuGet-Pakete geladen" -ForegroundColor Green
Write-Host ""

# Build
Write-Host "🔨 Kompiliere Anwendung..." -ForegroundColor Yellow
dotnet build $ProjectFile --configuration $Configuration --no-restore --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Error "Fehler beim Kompilieren der Anwendung"
    exit 1
}
Write-Host "  ✓ Anwendung erfolgreich kompiliert" -ForegroundColor Green
Write-Host ""

# Publish (Self-contained)
if ($Publish) {
    Write-Host "📋 Erstelle eigenständige Anwendung..." -ForegroundColor Yellow
    
    dotnet publish $ProjectFile `
        --configuration $Configuration `
        --output $PublishPath `
        --self-contained true `
        --runtime win-x64 `
        --verbosity quiet `
        /p:PublishSingleFile=true `
        /p:IncludeNativeLibrariesForSelfExtract=true
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Fehler beim Erstellen der eigenständigen Anwendung"
        exit 1
    }
    
    Write-Host "  ✓ Eigenständige Anwendung erstellt" -ForegroundColor Green
    Write-Host "  📁 Ausgabepfad: $PublishPath" -ForegroundColor Gray
    Write-Host ""
    
    # Kopiere PowerShell-Module
    Write-Host "📂 Kopiere PowerShell-Module..." -ForegroundColor Yellow
    
    $ModulesSource = Join-Path (Split-Path $ProjectPath -Parent) "Modules"
    $ConfigSource = Join-Path (Split-Path $ProjectPath -Parent) "Config"
    
    $ModulesTarget = Join-Path $PublishPath "Modules"
    $ConfigTarget = Join-Path $PublishPath "Config"
    
    if (Test-Path $ModulesSource) {
        Copy-Item $ModulesSource $ModulesTarget -Recurse -Force
        Write-Host "  ✓ Module kopiert" -ForegroundColor Green
    }
    
    if (Test-Path $ConfigSource) {
        Copy-Item $ConfigSource $ConfigTarget -Recurse -Force
        Write-Host "  ✓ Konfiguration kopiert" -ForegroundColor Green
    }
    
    Write-Host ""
}

# Ergebnis anzeigen
Write-Host "🎉 Build erfolgreich abgeschlossen!" -ForegroundColor Green
Write-Host ""

if ($Publish) {
    $exePath = Join-Path $PublishPath "ServerManagementApp.exe"
    if (Test-Path $exePath) {
        Write-Host "📋 ANWENDUNG BEREIT:" -ForegroundColor Cyan
        Write-Host "  Ausführbare Datei: $exePath" -ForegroundColor White
        Write-Host "  Größe: $([math]::Round((Get-Item $exePath).Length / 1MB, 1)) MB" -ForegroundColor Gray
        Write-Host ""
        Write-Host "🚀 Starten Sie die Anwendung mit:" -ForegroundColor Cyan
        Write-Host "  $exePath" -ForegroundColor Yellow
    }
} else {
    $exePath = Join-Path $OutputPath "net8.0-windows" "ServerManagementApp.exe"
    if (Test-Path $exePath) {
        Write-Host "📋 DEBUG-VERSION BEREIT:" -ForegroundColor Cyan
        Write-Host "  Ausführbare Datei: $exePath" -ForegroundColor White
        Write-Host ""
        Write-Host "🚀 Starten Sie die Anwendung mit:" -ForegroundColor Cyan
        Write-Host "  dotnet run --project $ProjectFile" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "📚 Weitere Optionen:" -ForegroundColor Cyan
Write-Host "  .\build.ps1 -Clean          # Bereinigt Build-Verzeichnisse" -ForegroundColor Gray
Write-Host "  .\build.ps1 -Publish        # Erstellt eigenständige Anwendung" -ForegroundColor Gray
Write-Host "  .\build.ps1 -Configuration Debug  # Debug-Build" -ForegroundColor Gray
