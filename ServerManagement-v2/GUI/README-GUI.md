# 🖥️ Windows Server Management GUI v2.0

Moderne grafische Benutzeroberfläche für die Windows Server-Verwaltung mit WPF und Material Design.

## 🎯 Features

### ✨ Moderne Benutzeroberfläche
- **Material Design**: Moderne, intuitive Oberfläche
- **Responsive Layout**: Anpassungsfähiges Design
- **Dark/Light Theme**: Automatische Theme-Erkennung
- **Echtzeit-Updates**: Live-Status der Server
- **Progress Indicators**: Visuelle Fortschrittsanzeigen

### 🚀 Funktionen
- **Server-Übersicht**: Alle Server auf einen Blick
- **Ping-Tests**: Schnelle Konnektivitätsprüfung
- **Windows Updates**: Suchen und installieren
- **Server-Neustarts**: Einzeln oder geordnet alle
- **Detaillierte Informationen**: RAM, Festplatten, Services
- **Aktivitätsprotokoll**: Alle Aktionen werden protokolliert

### 🔧 Technische Features
- **PowerShell Integration**: Nutzt bestehende PowerShell-Module
- **Parallele Verarbeitung**: Mehrere Server gleichzeitig
- **Sichere Authentifizierung**: Credential-Management
- **Fehlerbehandlung**: Robuste Fehlerbehandlung
- **Logging**: Detaillierte Protokollierung

## 📋 Systemanforderungen

### Entwicklung
- **Windows 11** (empfohlen) oder Windows 10
- **.NET 8 SDK** oder höher
- **Visual Studio 2022** oder **Visual Studio Code**
- **PowerShell 7+**

### Ausführung
- **Windows 11** (empfohlen) oder Windows 10
- **.NET 8 Runtime** (bei Self-Contained Build nicht erforderlich)
- **PowerShell 7+**
- **WinRM** aktiviert auf Zielservern

## 🛠️ Installation & Build

### 1. Voraussetzungen installieren

#### .NET 8 SDK
```powershell
# Mit winget
winget install Microsoft.DotNet.SDK.8

# Oder manuell von https://dotnet.microsoft.com/download
```

#### PowerShell 7
```powershell
# Mit winget
winget install Microsoft.PowerShell
```

### 2. Projekt klonen/herunterladen
```powershell
# Navigieren Sie zum GUI-Verzeichnis
cd ServerManagement-v2\GUI
```

### 3. Anwendung kompilieren

#### Einfacher Build
```powershell
.\build.ps1
```

#### Eigenständige Anwendung (empfohlen)
```powershell
.\build.ps1 -Publish
```

#### Debug-Build
```powershell
.\build.ps1 -Configuration Debug
```

#### Clean Build
```powershell
.\build.ps1 -Clean -Publish
```

## 🚀 Verwendung

### 1. Anwendung starten

#### Nach Publish-Build
```powershell
# Eigenständige Anwendung
.\publish\ServerManagementApp.exe
```

#### Nach normalem Build
```powershell
# Mit .NET Runtime
dotnet run --project ServerManagementApp.csproj
```

### 2. Erste Schritte

1. **Anmeldedaten eingeben**
   - Klicken Sie auf das Benutzer-Symbol in der Kopfzeile
   - Geben Sie Ihre Administrator-Anmeldedaten ein
   - Standard-Benutzer: `edv`

2. **Server-Status prüfen**
   - Die Anwendung lädt automatisch alle konfigurierten Server
   - Klicken Sie auf "Aktualisieren" für einen manuellen Refresh

3. **Aktionen ausführen**
   - Wählen Sie Server in der Seitenleiste aus
   - Verwenden Sie die Aktions-Buttons für verschiedene Aufgaben

## 🎮 Benutzeroberfläche

### Hauptfenster
```
┌─────────────────────────────────────────────────────────┐
│ [🖥️] Windows Server Management v2.0    [👤][⚙️][🔄] │
├─────────────────┬───────────────────────────────────────┤
│ AKTIONEN        │ Server Übersicht                      │
│ • Server anping │ ┌─────────────────────────────────────┐ │
│ • Updates such  │ │ ✓ DC01    DomainController          │ │
│ • Updates inst  │ │   Online (5ms)                      │ │
│ • Server neust  │ └─────────────────────────────────────┘ │
│ • Server-Status │ ┌─────────────────────────────────────┐ │
│                 │ │ ✓ DC02    DomainController          │ │
│ SERVER AUSWAHL  │ │   Online (3ms)                      │ │
│ ☑ Alle Server   │ └─────────────────────────────────────┘ │
│ ☑ DC01          │                                       │
│ ☑ DC02          │ [Aktivitätsprotokoll]                 │
│ ☑ DB01          │                                       │
│ ...             │                                       │
├─────────────────┴───────────────────────────────────────┤
│ Bereit                              23.06.2025 14:30:15 │
└─────────────────────────────────────────────────────────┘
```

### Server-Karten
Jede Server-Karte zeigt:
- **Status-Icon**: ✓ (Online), ✗ (Offline), ⚠ (Warnung)
- **Server-Name**: z.B. "DC01"
- **Rolle**: z.B. "DomainController"
- **Status-Text**: z.B. "Online (5ms)"
- **Aktions-Buttons**: Details anzeigen, Neustart

### Aktivitätsprotokoll
```
[14:30:15] [INFO   ] Server Management initialisiert
[14:30:16] [SUCCESS] Server DC01: Online (5ms)
[14:30:16] [SUCCESS] Server DC02: Online (3ms)
[14:30:17] [INFO   ] Konnektivitätstest abgeschlossen: 7/7 Server online
```

## ⚙️ Konfiguration

### Server-Konfiguration
Die Anwendung verwendet die gleiche `ServerConfig.psd1` wie das PowerShell-Skript:

```powershell
# ServerManagement-v2\Config\ServerConfig.psd1
@{
    ServerList = @(
        @{
            Name = "DC01"
            Role = "DomainController"
            Priority = 1
            RestartDelay = 180
        },
        # ... weitere Server
    )
}
```

### Anwendungseinstellungen
- **Theme**: Automatisch basierend auf Windows-Einstellungen
- **Refresh-Intervall**: Manuell oder automatisch
- **Logging**: Alle Aktionen werden protokolliert
- **Credentials**: Temporär im Arbeitsspeicher gespeichert

## 🔧 Entwicklung

### Projektstruktur
```
GUI/
├── ServerManagementApp.csproj    # Projekt-Datei
├── App.xaml                      # Anwendungs-Konfiguration
├── App.xaml.cs                   # Anwendungs-Code
├── MainWindow.xaml               # Hauptfenster UI
├── MainWindow.xaml.cs            # Hauptfenster Code
├── Models/
│   └── ServerInfo.cs             # Datenmodelle
├── Services/
│   └── PowerShellService.cs      # PowerShell-Integration
├── Views/
│   └── CredentialsDialog.xaml    # Anmeldedaten-Dialog
├── build.ps1                     # Build-Skript
└── README-GUI.md                 # Diese Datei
```

### Verwendete Technologien
- **WPF (Windows Presentation Foundation)**: UI-Framework
- **Material Design in XAML**: Moderne UI-Komponenten
- **System.Management.Automation**: PowerShell-Integration
- **.NET 8**: Aktuelle .NET-Version
- **MVVM Pattern**: Saubere Trennung von UI und Logik

### Debugging
```powershell
# Debug-Build erstellen
.\build.ps1 -Configuration Debug

# Mit Visual Studio debuggen
# Oder mit VS Code + C# Extension
```

## 🚨 Troubleshooting

### Häufige Probleme

#### .NET 8 nicht gefunden
```
Fehler: .NET 8 SDK ist nicht installiert
Lösung: winget install Microsoft.DotNet.SDK.8
```

#### PowerShell-Module nicht gefunden
```
Fehler: ServerManagement.psm1 nicht gefunden
Lösung: Stellen Sie sicher, dass die Module im richtigen Pfad sind
```

#### WinRM-Verbindungsfehler
```
Fehler: Server nicht erreichbar
Lösung: WinRM auf Zielservern aktivieren
```

### Log-Dateien
- **Anwendungs-Log**: Im Aktivitätsprotokoll der Anwendung
- **PowerShell-Log**: `%TEMP%\ServerManagement_YYYYMMDD.log`

## 🎯 Roadmap

### Geplante Features
- [ ] **Server-Details-Dialog**: Detaillierte Ansicht pro Server
- [ ] **Einstellungen-Dialog**: Konfigurierbare Optionen
- [ ] **Berichte**: Export von Server-Status und Logs
- [ ] **Benachrichtigungen**: Toast-Notifications
- [ ] **Themes**: Anpassbare Farbschemata
- [ ] **Automatischer Refresh**: Konfigurierbare Intervalle
- [ ] **Favoriten**: Häufig verwendete Server-Gruppen

### Verbesserungen
- [ ] **Performance**: Optimierung für viele Server
- [ ] **Caching**: Zwischenspeicherung von Server-Daten
- [ ] **Offline-Modus**: Funktionalität ohne Netzwerk
- [ ] **Multi-Language**: Unterstützung für mehrere Sprachen

## 📞 Support

Bei Problemen oder Fragen:
1. Prüfen Sie die Logs im Aktivitätsprotokoll
2. Überprüfen Sie die PowerShell-Module
3. Testen Sie die Konnektivität zu den Servern
4. Kontaktieren Sie den Administrator

---

**Entwickelt von Tonino Gerns**  
**Version 2.0.0 - 2025**
