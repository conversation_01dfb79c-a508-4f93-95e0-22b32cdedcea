using System;
using System.ComponentModel;
using System.Windows.Media;

namespace ServerManagementApp.Models
{
    public class ServerInfo : INotifyPropertyChanged
    {
        private string _name;
        private string _role;
        private int _priority;
        private bool _isOnline;
        private string _statusText;
        private string _statusIcon;
        private Brush _statusColor;
        private int? _responseTime;
        private DateTime? _lastChecked;
        private string _ipAddress;
        private ServerDetails _details;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        public string Role
        {
            get => _role;
            set
            {
                _role = value;
                OnPropertyChanged(nameof(Role));
            }
        }

        public int Priority
        {
            get => _priority;
            set
            {
                _priority = value;
                OnPropertyChanged(nameof(Priority));
            }
        }

        public bool IsOnline
        {
            get => _isOnline;
            set
            {
                _isOnline = value;
                OnPropertyChanged(nameof(IsOnline));
            }
        }

        public string StatusText
        {
            get => _statusText;
            set
            {
                _statusText = value;
                OnPropertyChanged(nameof(StatusText));
            }
        }

        public string StatusIcon
        {
            get => _statusIcon;
            set
            {
                _statusIcon = value;
                OnPropertyChanged(nameof(StatusIcon));
            }
        }

        public Brush StatusColor
        {
            get => _statusColor;
            set
            {
                _statusColor = value;
                OnPropertyChanged(nameof(StatusColor));
            }
        }

        public int? ResponseTime
        {
            get => _responseTime;
            set
            {
                _responseTime = value;
                OnPropertyChanged(nameof(ResponseTime));
            }
        }

        public DateTime? LastChecked
        {
            get => _lastChecked;
            set
            {
                _lastChecked = value;
                OnPropertyChanged(nameof(LastChecked));
            }
        }

        public string IpAddress
        {
            get => _ipAddress;
            set
            {
                _ipAddress = value;
                OnPropertyChanged(nameof(IpAddress));
            }
        }

        public ServerDetails Details
        {
            get => _details;
            set
            {
                _details = value;
                OnPropertyChanged(nameof(Details));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ServerDetails
    {
        public string OperatingSystem { get; set; }
        public string Version { get; set; }
        public DateTime LastBootTime { get; set; }
        public double TotalMemoryGB { get; set; }
        public double FreeMemoryMB { get; set; }
        public string Manufacturer { get; set; }
        public string Model { get; set; }
        public DiskInfo[] Disks { get; set; }
        public ServiceInfo[] StoppedServices { get; set; }
        public TimeSpan Uptime => DateTime.Now - LastBootTime;
    }

    public class DiskInfo
    {
        public string Drive { get; set; }
        public string Label { get; set; }
        public string DriveType { get; set; }
        public string FileSystem { get; set; }
        public double TotalSpaceGB { get; set; }
        public double FreeSpaceGB { get; set; }
        public double UsedSpaceGB { get; set; }
        public double UsedPercent { get; set; }
        public string Status { get; set; }
    }

    public class ServiceInfo
    {
        public string Name { get; set; }
        public string DisplayName { get; set; }
        public string Status { get; set; }
        public string StartType { get; set; }
    }

    public class ServerSelectionItem : INotifyPropertyChanged
    {
        private string _name;
        private bool _isSelected;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged(nameof(Name));
            }
        }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged(nameof(IsSelected));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ServerConfigItem
    {
        public string Name { get; set; }
        public string Role { get; set; }
        public int Priority { get; set; }
        public int RestartDelay { get; set; }
    }

    public class ConnectivityResult
    {
        public string ServerName { get; set; }
        public bool IsOnline { get; set; }
        public int? ResponseTime { get; set; }
        public string IpAddress { get; set; }
        public string Error { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class UpdateInfo
    {
        public string Title { get; set; }
        public string Description { get; set; }
        public double SizeMB { get; set; }
        public bool IsDownloaded { get; set; }
        public string Categories { get; set; }
        public string KBArticleIDs { get; set; }
        public bool RebootRequired { get; set; }
    }

    public class UpdateInstallResult
    {
        public string Status { get; set; }
        public int UpdatesInstalled { get; set; }
        public bool RebootRequired { get; set; }
        public string Error { get; set; }
    }

    public class RestartResult
    {
        public string ServerName { get; set; }
        public bool Success { get; set; }
        public double TotalTimeSeconds { get; set; }
        public string Error { get; set; }
    }

    public enum LogLevel
    {
        Info,
        Success,
        Warning,
        Error,
        Debug
    }
}
