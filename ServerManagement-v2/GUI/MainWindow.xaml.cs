using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using ServerManagementApp.Models;
using ServerManagementApp.Services;

namespace ServerManagementApp
{
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private readonly PowerShellService _powerShellService;
        private readonly DispatcherTimer _timer;
        private ObservableCollection<ServerInfo> _servers;
        private ObservableCollection<ServerSelectionItem> _serverSelection;

        public ObservableCollection<ServerInfo> Servers
        {
            get => _servers;
            set
            {
                _servers = value;
                OnPropertyChanged(nameof(Servers));
            }
        }

        public ObservableCollection<ServerSelectionItem> ServerSelection
        {
            get => _serverSelection;
            set
            {
                _serverSelection = value;
                OnPropertyChanged(nameof(ServerSelection));
            }
        }

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;

            _powerShellService = new PowerShellService();
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += Timer_Tick;
            _timer.Start();

            Servers = new ObservableCollection<ServerInfo>();
            ServerSelection = new ObservableCollection<ServerSelectionItem>();

            ServerOverviewList.ItemsSource = Servers;
            ServerSelectionList.ItemsSource = ServerSelection;

            Loaded += MainWindow_Loaded;
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await InitializeAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Fehler beim Initialisieren:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                    "Initialisierungsfehler", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task InitializeAsync()
        {
            ShowLoading("Initialisiere Server Management...");

            try
            {
                // Load server configuration
                ServerConfigItem[] serverConfig;
                try
                {
                    serverConfig = await _powerShellService.LoadServerConfigurationAsync();
                }
                catch (Exception ex)
                {
                    AddLogEntry($"Warnung: Konfiguration konnte nicht geladen werden: {ex.Message}", LogLevel.Warning);
                    // Fallback: Standard-Server erstellen
                    serverConfig = new[]
                    {
                        new ServerConfigItem { Name = "DC01", Role = "DomainController", Priority = 1, RestartDelay = 180 },
                        new ServerConfigItem { Name = "DC02", Role = "DomainController", Priority = 2, RestartDelay = 120 },
                        new ServerConfigItem { Name = "DB01", Role = "DatabaseServer", Priority = 3, RestartDelay = 180 },
                        new ServerConfigItem { Name = "APP01", Role = "ApplicationServer", Priority = 4, RestartDelay = 120 },
                        new ServerConfigItem { Name = "MAIL", Role = "MailServer", Priority = 5, RestartDelay = 120 },
                        new ServerConfigItem { Name = "FILE", Role = "FileServer", Priority = 6, RestartDelay = 90 },
                        new ServerConfigItem { Name = "TS01", Role = "TerminalServer", Priority = 7, RestartDelay = 0 }
                    };
                }
                
                // Initialize server list
                Servers.Clear();
                ServerSelection.Clear();

                foreach (var server in serverConfig)
                {
                    var serverInfo = new ServerInfo
                    {
                        Name = server.Name,
                        Role = server.Role,
                        Priority = server.Priority,
                        IsOnline = false,
                        StatusText = "Unbekannt",
                        StatusIcon = "❓",
                        StatusColor = System.Windows.Media.Brushes.Gray
                    };

                    Servers.Add(serverInfo);
                    ServerSelection.Add(new ServerSelectionItem { Name = server.Name, IsSelected = false });
                }

                AddLogEntry("Server Management initialisiert", LogLevel.Info);
                UpdateStatusText($"{Servers.Count} Server geladen");

                // Initial ping test (optional, nur wenn PowerShell funktioniert)
                try
                {
                    await RefreshServerStatusAsync();
                }
                catch (Exception ex)
                {
                    AddLogEntry($"Warnung: Server-Status konnte nicht abgerufen werden: {ex.Message}", LogLevel.Warning);
                    UpdateStatusText($"{Servers.Count} Server geladen (Status unbekannt)");
                }
            }
            catch (Exception ex)
            {
                AddLogEntry($"Fehler bei der Initialisierung: {ex.Message}", LogLevel.Error);
                MessageBox.Show($"Fehler bei der Initialisierung:\n{ex.Message}", "Fehler", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                HideLoading();
            }
        }

        private async Task RefreshServerStatusAsync()
        {
            ShowLoading("Aktualisiere Server-Status...");

            try
            {
                var credentials = await _powerShellService.GetCredentialsAsync();
                if (credentials == null)
                {
                    AddLogEntry("Keine Anmeldedaten verfügbar", LogLevel.Warning);
                    return;
                }

                var tasks = Servers.Select(async server =>
                {
                    try
                    {
                        var result = await _powerShellService.TestServerConnectivityAsync(server.Name);
                        
                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            server.IsOnline = result.IsOnline;
                            server.ResponseTime = result.ResponseTime;
                            server.LastChecked = DateTime.Now;

                            if (result.IsOnline)
                            {
                                server.StatusText = $"Online ({result.ResponseTime}ms)";
                                server.StatusIcon = "✅";
                                server.StatusColor = System.Windows.Media.Brushes.Green;
                            }
                            else
                            {
                                server.StatusText = "Offline";
                                server.StatusIcon = "❌";
                                server.StatusColor = System.Windows.Media.Brushes.Red;
                            }
                        });

                        AddLogEntry($"Server {server.Name}: {server.StatusText}", 
                            result.IsOnline ? LogLevel.Success : LogLevel.Warning);
                    }
                    catch (Exception ex)
                    {
                        AddLogEntry($"Fehler beim Testen von {server.Name}: {ex.Message}", LogLevel.Error);
                    }
                });

                await Task.WhenAll(tasks);

                var onlineCount = Servers.Count(s => s.IsOnline);
                UpdateStatusText($"{onlineCount}/{Servers.Count} Server online");
            }
            catch (Exception ex)
            {
                AddLogEntry($"Fehler beim Aktualisieren des Server-Status: {ex.Message}", LogLevel.Error);
            }
            finally
            {
                HideLoading();
            }
        }

        private void ShowLoading(string message)
        {
            LoadingText.Text = message;
            LoadingOverlay.Visibility = Visibility.Visible;
            ProgressBar.Visibility = Visibility.Visible;
        }

        private void HideLoading()
        {
            LoadingOverlay.Visibility = Visibility.Collapsed;
            ProgressBar.Visibility = Visibility.Collapsed;
        }

        private void UpdateStatusText(string text)
        {
            StatusText.Text = text;
        }

        private void AddLogEntry(string message, LogLevel level)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var levelText = level.ToString().ToUpper().PadRight(7);
            var logEntry = $"[{timestamp}] [{levelText}] {message}\n";

            Application.Current.Dispatcher.Invoke(() =>
            {
                LogTextBox.AppendText(logEntry);
                LogScrollViewer.ScrollToEnd();
            });
        }

        private void Timer_Tick(object sender, EventArgs e)
        {
            TimeText.Text = DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss");
        }

        // Event Handlers
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshServerStatusAsync();
        }

        private async void PingAllButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshServerStatusAsync();
        }

        private async void SearchUpdatesButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedServers = GetSelectedServers();
            if (!selectedServers.Any())
            {
                MessageBox.Show("Bitte wählen Sie mindestens einen Server aus.", "Keine Auswahl", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            ShowLoading("Suche nach Windows Updates...");
            
            try
            {
                var credentials = await _powerShellService.GetCredentialsAsync();
                if (credentials == null) return;

                foreach (var serverName in selectedServers)
                {
                    try
                    {
                        var updates = await _powerShellService.SearchWindowsUpdatesAsync(serverName, credentials);
                        AddLogEntry($"Server {serverName}: {updates.Count} Updates gefunden", LogLevel.Info);
                    }
                    catch (Exception ex)
                    {
                        AddLogEntry($"Fehler bei Update-Suche auf {serverName}: {ex.Message}", LogLevel.Error);
                    }
                }
            }
            finally
            {
                HideLoading();
            }
        }

        private async void InstallUpdatesButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedServers = GetSelectedServers();
            if (!selectedServers.Any())
            {
                MessageBox.Show("Bitte wählen Sie mindestens einen Server aus.", "Keine Auswahl", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show(
                "Möchten Sie Windows Updates auf den ausgewählten Servern installieren?\n\nWICHTIG: Updates werden OHNE automatischen Neustart installiert.",
                "Updates installieren", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result != MessageBoxResult.Yes) return;

            ShowLoading("Installiere Windows Updates...");
            
            try
            {
                var credentials = await _powerShellService.GetCredentialsAsync();
                if (credentials == null) return;

                foreach (var serverName in selectedServers)
                {
                    try
                    {
                        var installResult = await _powerShellService.InstallWindowsUpdatesAsync(serverName, credentials);
                        AddLogEntry($"Server {serverName}: {installResult.UpdatesInstalled} Updates installiert", LogLevel.Success);
                        
                        if (installResult.RebootRequired)
                        {
                            AddLogEntry($"Server {serverName}: Neustart erforderlich", LogLevel.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLogEntry($"Fehler bei Update-Installation auf {serverName}: {ex.Message}", LogLevel.Error);
                    }
                }
            }
            finally
            {
                HideLoading();
            }
        }

        private async void RestartServersButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedServers = GetSelectedServers();
            if (!selectedServers.Any())
            {
                MessageBox.Show("Bitte wählen Sie mindestens einen Server aus.", "Keine Auswahl", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var message = selectedServers.Length == Servers.Count
                ? "Möchten Sie ALLE Server in der korrekten Reihenfolge neu starten?\n\nReihenfolge: DC01 → DC02 → DB01 → APP01 → MAIL → FILE → TS01"
                : $"Möchten Sie die {selectedServers.Length} ausgewählten Server neu starten?";

            var result = MessageBox.Show(message, "Server neu starten", 
                MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result != MessageBoxResult.Yes) return;

            ShowLoading("Starte Server neu...");
            
            try
            {
                var credentials = await _powerShellService.GetCredentialsAsync();
                if (credentials == null) return;

                if (selectedServers.Length == Servers.Count)
                {
                    // Restart all servers in order
                    await _powerShellService.RestartAllServersOrderedAsync(credentials);
                }
                else
                {
                    // Restart selected servers
                    foreach (var serverName in selectedServers)
                    {
                        try
                        {
                            var restartResult = await _powerShellService.RestartServerAsync(serverName, credentials);
                            AddLogEntry($"Server {serverName}: Neustart {(restartResult.Success ? "erfolgreich" : "fehlgeschlagen")}",
                                restartResult.Success ? LogLevel.Success : LogLevel.Error);
                        }
                        catch (Exception ex)
                        {
                            AddLogEntry($"Fehler beim Neustart von {serverName}: {ex.Message}", LogLevel.Error);
                        }
                    }
                }

                // Refresh status after restart
                await Task.Delay(5000); // Wait a bit before checking
                await RefreshServerStatusAsync();
            }
            finally
            {
                HideLoading();
            }
        }

        private async void ServerStatusButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedServers = GetSelectedServers();
            if (!selectedServers.Any())
            {
                MessageBox.Show("Bitte wählen Sie mindestens einen Server aus.", "Keine Auswahl", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            ShowLoading("Lade Server-Details...");
            
            try
            {
                var credentials = await _powerShellService.GetCredentialsAsync();
                if (credentials == null) return;

                foreach (var serverName in selectedServers)
                {
                    try
                    {
                        var details = await _powerShellService.GetServerDetailsAsync(serverName, credentials);
                        // TODO: Show details in a dialog or new tab
                        AddLogEntry($"Server {serverName}: Details geladen", LogLevel.Info);
                    }
                    catch (Exception ex)
                    {
                        AddLogEntry($"Fehler beim Laden der Details von {serverName}: {ex.Message}", LogLevel.Error);
                    }
                }
            }
            finally
            {
                HideLoading();
            }
        }

        private void CredentialsButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Show credentials dialog
            AddLogEntry("Anmeldedaten-Dialog geöffnet", LogLevel.Info);
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Show settings dialog
            AddLogEntry("Einstellungen-Dialog geöffnet", LogLevel.Info);
        }

        private void ShowServerDetails_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ServerInfo server)
            {
                // TODO: Show server details dialog
                AddLogEntry($"Details für {server.Name} angezeigt", LogLevel.Info);
            }
        }

        private async void RestartSingleServer_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ServerInfo server)
            {
                var result = MessageBox.Show($"Möchten Sie den Server {server.Name} neu starten?", 
                    "Server neu starten", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    ShowLoading($"Starte {server.Name} neu...");
                    
                    try
                    {
                        var credentials = await _powerShellService.GetCredentialsAsync();
                        if (credentials != null)
                        {
                            var restartResult = await _powerShellService.RestartServerAsync(server.Name, credentials);
                            AddLogEntry($"Server {server.Name}: Neustart {(restartResult.Success ? "erfolgreich" : "fehlgeschlagen")}",
                                restartResult.Success ? LogLevel.Success : LogLevel.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLogEntry($"Fehler beim Neustart von {server.Name}: {ex.Message}", LogLevel.Error);
                    }
                    finally
                    {
                        HideLoading();
                    }
                }
            }
        }

        private void SelectAllCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            foreach (var item in ServerSelection)
            {
                item.IsSelected = true;
            }
        }

        private void SelectAllCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            foreach (var item in ServerSelection)
            {
                item.IsSelected = false;
            }
        }

        private void ClearLogButton_Click(object sender, RoutedEventArgs e)
        {
            LogTextBox.Clear();
            AddLogEntry("Log gelöscht", LogLevel.Info);
        }

        private void SaveLogButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement log saving
            AddLogEntry("Log gespeichert", LogLevel.Info);
        }

        private string[] GetSelectedServers()
        {
            return ServerSelection.Where(s => s.IsSelected).Select(s => s.Name).ToArray();
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
