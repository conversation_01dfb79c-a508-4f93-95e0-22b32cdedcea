using System;
using System.Management.Automation;
using System.Security;
using System.Windows;

namespace ServerManagementApp.Views
{
    public partial class CredentialsDialog : Window
    {
        public PSCredential Credentials { get; private set; }

        public CredentialsDialog()
        {
            InitializeComponent();
            UsernameTextBox.Focus();
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                MessageBox.Show("Bitte geben Sie einen Benutzernamen ein.", "Eingabe erforderlich", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                UsernameTextBox.Focus();
                return;
            }

            if (PasswordBox.SecurePassword.Length == 0)
            {
                MessageBox.Show("Bitte geben Sie ein Passwort ein.", "Eingabe erforderlich", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                PasswordBox.Focus();
                return;
            }

            try
            {
                Credentials = new PSCredential(UsernameTextBox.Text, PasswordBox.SecurePassword);
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Fehler beim <PERSON> der Anmeldedaten: {ex.Message}", "Fehler", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // Set owner to main window if available
            if (Application.Current.MainWindow != this && Application.Current.MainWindow != null)
            {
                Owner = Application.Current.MainWindow;
            }
        }
    }
}
