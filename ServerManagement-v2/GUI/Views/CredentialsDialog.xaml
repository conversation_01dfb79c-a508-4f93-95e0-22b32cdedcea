<Window x:Class="ServerManagementApp.Views.CredentialsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Anmeldedaten" 
        Height="350" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="Account" Width="32" Height="32" 
                                     VerticalAlignment="Center" 
                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="Anmeldedaten für Server-Verwaltung" 
                       FontSize="18" FontWeight="SemiBold" 
                       VerticalAlignment="Center" Margin="12,0"/>
        </StackPanel>

        <!-- Info Text -->
        <TextBlock Grid.Row="1" 
                   Text="Geben Sie Ihre Administrator-Anmeldedaten ein, um auf die Server zugreifen zu können."
                   TextWrapping="Wrap" 
                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                   Margin="0,0,0,24"/>

        <!-- Input Fields -->
        <StackPanel Grid.Row="2">
            <TextBox x:Name="UsernameTextBox" 
                     materialDesign:HintAssist.Hint="Benutzername"
                     materialDesign:HintAssist.IsFloating="True"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     Margin="0,0,0,16"
                     Text="edv"/>

            <PasswordBox x:Name="PasswordBox" 
                         materialDesign:HintAssist.Hint="Passwort"
                         materialDesign:HintAssist.IsFloating="True"
                         Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                         Margin="0,0,0,16"/>

            <CheckBox x:Name="RememberCheckBox" 
                      Content="Anmeldedaten für diese Sitzung speichern"
                      IsChecked="True"
                      Margin="0,8,0,0"/>

            <TextBlock Text="Hinweis: Anmeldedaten werden nur temporär im Arbeitsspeicher gespeichert und nicht dauerhaft gesichert."
                       FontSize="11"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       TextWrapping="Wrap"
                       Margin="0,8,0,0"/>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,24,0,0">
            <Button x:Name="CancelButton" 
                    Content="Abbrechen" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="CancelButton_Click"
                    Margin="0,0,8,0"/>
            <Button x:Name="OkButton" 
                    Content="OK" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Click="OkButton_Click"
                    IsDefault="True"/>
        </StackPanel>
    </Grid>
</Window>
