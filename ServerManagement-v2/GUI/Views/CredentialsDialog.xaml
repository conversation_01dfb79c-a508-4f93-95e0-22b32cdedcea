<Window x:Class="ServerManagementApp.Views.CredentialsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Anmeldedaten"
        Height="350" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="White"
        FontFamily="Segoe UI"
        FontSize="13">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <TextBlock Text="👤" FontSize="32"
                       VerticalAlignment="Center"
                       Foreground="#2196F3"/>
            <TextBlock Text="Anmeldedaten für Server-Verwaltung"
                       FontSize="18" FontWeight="SemiBold"
                       VerticalAlignment="Center" Margin="12,0"/>
        </StackPanel>

        <!-- Info Text -->
        <TextBlock Grid.Row="1"
                   Text="Geben Sie Ihre Administrator-Anmeldedaten ein, um auf die Server zugreifen zu können."
                   TextWrapping="Wrap"
                   Foreground="#757575"
                   Margin="0,0,0,24"/>

        <!-- Input Fields -->
        <StackPanel Grid.Row="2">
            <Label Content="Benutzername:" Margin="0,0,0,4"/>
            <TextBox x:Name="UsernameTextBox"
                     Padding="8"
                     BorderBrush="#E0E0E0"
                     Margin="0,0,0,16"
                     Text="edv"/>

            <Label Content="Passwort:" Margin="0,0,0,4"/>
            <PasswordBox x:Name="PasswordBox"
                         Padding="8"
                         BorderBrush="#E0E0E0"
                         Margin="0,0,0,16"/>

            <CheckBox x:Name="RememberCheckBox"
                      Content="Anmeldedaten für diese Sitzung speichern"
                      IsChecked="True"
                      Margin="0,8,0,0"/>

            <TextBlock Text="Hinweis: Anmeldedaten werden nur temporär im Arbeitsspeicher gespeichert und nicht dauerhaft gesichert."
                       FontSize="11"
                       Foreground="#757575"
                       TextWrapping="Wrap"
                       Margin="0,8,0,0"/>
        </StackPanel>

        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal"
                    HorizontalAlignment="Right" Margin="0,24,0,0">
            <Button x:Name="CancelButton"
                    Content="Abbrechen"
                    Padding="16,8"
                    Background="#E0E0E0"
                    BorderThickness="0"
                    Click="CancelButton_Click"
                    Margin="0,0,8,0"/>
            <Button x:Name="OkButton"
                    Content="OK"
                    Padding="16,8"
                    Background="#2196F3"
                    Foreground="White"
                    BorderThickness="0"
                    Click="OkButton_Click"
                    IsDefault="True"/>
        </StackPanel>
    </Grid>
</Window>
