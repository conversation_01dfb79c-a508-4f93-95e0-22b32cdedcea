using System;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using System.Windows;

namespace TestApp
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            StatusText.Text = $"Gestartet um {DateTime.Now:HH:mm:ss}";
        }

        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            OutputText.Text = $"Test-Funktion ausgeführt um {DateTime.Now:HH:mm:ss}\n\n" +
                             "✅ GUI funktioniert!\n" +
                             "✅ Buttons funktionieren!\n" +
                             "✅ .NET 8 läuft korrekt!\n\n" +
                             "Das ist ein einfacher Test der GUI-Funktionalität.";
            
            StatusText.Text = "Test erfolgreich";
        }

        private async void PingButton_Click(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "Teste Server...";
            OutputText.Text = "Teste Server-Verbindungen...\n\n";

            string[] servers = { "DC01", "DC02", "DB01", "APP01", "MAIL", "FILE", "TS01" };
            
            foreach (string server in servers)
            {
                try
                {
                    using (var ping = new Ping())
                    {
                        var reply = await ping.SendPingAsync(server, 3000);
                        
                        if (reply.Status == IPStatus.Success)
                        {
                            OutputText.Text += $"✅ {server.PadRight(8)} Online  ({reply.RoundtripTime}ms)\n";
                        }
                        else
                        {
                            OutputText.Text += $"❌ {server.PadRight(8)} Offline ({reply.Status})\n";
                        }
                    }
                }
                catch (Exception ex)
                {
                    OutputText.Text += $"❌ {server.PadRight(8)} Fehler: {ex.Message}\n";
                }
            }
            
            OutputText.Text += "\nPing-Test abgeschlossen.";
            StatusText.Text = "Ping-Test fertig";
        }

        private void StatusButton_Click(object sender, RoutedEventArgs e)
        {
            OutputText.Text = "SYSTEM-STATUS\n" +
                             "═════════════\n\n" +
                             $"🖥️  Computer: {Environment.MachineName}\n" +
                             $"👤 Benutzer: {Environment.UserName}\n" +
                             $"🌐 Domain: {Environment.UserDomainName}\n" +
                             $"📁 Arbeitsverzeichnis: {Environment.CurrentDirectory}\n" +
                             $"⚙️  .NET Version: {Environment.Version}\n" +
                             $"💾 Arbeitsspeicher: {GC.GetTotalMemory(false) / 1024 / 1024:F1} MB\n" +
                             $"🕒 Systemzeit: {DateTime.Now:dd.MM.yyyy HH:mm:ss}\n" +
                             $"⏱️  Uptime: {TimeSpan.FromMilliseconds(Environment.TickCount):dd\\.hh\\:mm\\:ss}\n\n" +
                             "✅ GUI-Anwendung läuft erfolgreich!";
            
            StatusText.Text = "Status angezeigt";
        }
    }
}
