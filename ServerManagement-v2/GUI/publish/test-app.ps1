# Test-Skript um die GUI-Anwendung zu debuggen
try {
    Write-Host "Teste GUI-Anwendung..." -ForegroundColor Cyan
    
    # Prüfe ob alle Dateien vorhanden sind
    $exePath = ".\ServerManagementApp.exe"
    if (-not (Test-Path $exePath)) {
        Write-Host "FEHLER: ServerManagementApp.exe nicht gefunden!" -ForegroundColor Red
        exit 1
    }
    
    $configPath = ".\Config\ServerConfig.psd1"
    if (-not (Test-Path $configPath)) {
        Write-Host "FEHLER: ServerConfig.psd1 nicht gefunden!" -ForegroundColor Red
        exit 1
    }
    
    $modulePath = ".\Modules\ServerManagement.psm1"
    if (-not (Test-Path $modulePath)) {
        Write-Host "FEHLER: ServerManagement.psm1 nicht gefunden!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✓ Alle erforderlichen Dateien gefunden" -ForegroundColor Green
    
    # Teste PowerShell-Module
    try {
        Import-PowerShellDataFile -Path $configPath
        Write-Host "✓ Konfiguration erfolgreich geladen" -ForegroundColor Green
    }
    catch {
        Write-Host "FEHLER beim Laden der Konfiguration: $_" -ForegroundColor Red
    }
    
    # Starte Anwendung mit Fehlerbehandlung
    Write-Host "Starte GUI-Anwendung..." -ForegroundColor Yellow
    
    $process = Start-Process -FilePath $exePath -PassThru -WindowStyle Normal
    
    if ($process) {
        Write-Host "✓ Anwendung gestartet (PID: $($process.Id))" -ForegroundColor Green
        
        # Warte kurz und prüfe ob Prozess noch läuft
        Start-Sleep -Seconds 3
        
        if ($process.HasExited) {
            Write-Host "FEHLER: Anwendung wurde beendet (Exit Code: $($process.ExitCode))" -ForegroundColor Red
        } else {
            Write-Host "✓ Anwendung läuft erfolgreich" -ForegroundColor Green
        }
    } else {
        Write-Host "FEHLER: Anwendung konnte nicht gestartet werden" -ForegroundColor Red
    }
}
catch {
    Write-Host "FEHLER: $_" -ForegroundColor Red
    Write-Host "Stack Trace: $($_.ScriptStackTrace)" -ForegroundColor Gray
}
