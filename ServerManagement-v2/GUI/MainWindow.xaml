<Window x:Class="ServerManagementApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Windows Server Management v2.0"
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Server" Width="32" Height="32" VerticalAlignment="Center" Foreground="White"/>
                        <TextBlock Text="Windows Server Management v2.0" 
                                   FontSize="20" FontWeight="Bold" 
                                   VerticalAlignment="Center" Margin="12,0" 
                                   Foreground="White"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button x:Name="CredentialsButton" 
                                Style="{StaticResource MaterialDesignToolButton}"
                                ToolTip="Anmeldedaten konfigurieren"
                                Click="CredentialsButton_Click">
                            <materialDesign:PackIcon Kind="Account" Foreground="White"/>
                        </Button>
                        <Button x:Name="SettingsButton" 
                                Style="{StaticResource MaterialDesignToolButton}"
                                ToolTip="Einstellungen"
                                Click="SettingsButton_Click">
                            <materialDesign:PackIcon Kind="Settings" Foreground="White"/>
                        </Button>
                        <Button x:Name="RefreshButton" 
                                Style="{StaticResource MaterialDesignToolButton}"
                                ToolTip="Aktualisieren"
                                Click="RefreshButton_Click">
                            <materialDesign:PackIcon Kind="Refresh" Foreground="White"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Sidebar -->
                <materialDesign:Card Grid.Column="0" Margin="8" materialDesign:ShadowAssist.ShadowDepth="Depth2">
                    <StackPanel>
                        <!-- Action Buttons -->
                        <TextBlock Text="AKTIONEN" FontWeight="Bold" Margin="16,16,16,8" 
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        
                        <Button x:Name="PingAllButton" Style="{StaticResource ActionButton}" 
                                Click="PingAllButton_Click" Margin="16,4">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Wifi" Margin="0,0,8,0"/>
                                <TextBlock Text="Server anpingen"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SearchUpdatesButton" Style="{StaticResource ActionButton}" 
                                Click="SearchUpdatesButton_Click" Margin="16,4">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Magnify" Margin="0,0,8,0"/>
                                <TextBlock Text="Updates suchen"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InstallUpdatesButton" Style="{StaticResource ActionButton}" 
                                Click="InstallUpdatesButton_Click" Margin="16,4">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Download" Margin="0,0,8,0"/>
                                <TextBlock Text="Updates installieren"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="RestartServersButton" Style="{StaticResource ActionButton}" 
                                Click="RestartServersButton_Click" Margin="16,4">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Restart" Margin="0,0,8,0"/>
                                <TextBlock Text="Server neu starten"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ServerStatusButton" Style="{StaticResource ActionButton}" 
                                Click="ServerStatusButton_Click" Margin="16,4">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Information" Margin="0,0,8,0"/>
                                <TextBlock Text="Server-Status"/>
                            </StackPanel>
                        </Button>

                        <Separator Margin="16,16"/>

                        <!-- Server Selection -->
                        <TextBlock Text="SERVER AUSWAHL" FontWeight="Bold" Margin="16,8" 
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        
                        <CheckBox x:Name="SelectAllCheckBox" Content="Alle Server auswählen" 
                                  Margin="16,8" Checked="SelectAllCheckBox_Checked" 
                                  Unchecked="SelectAllCheckBox_Unchecked"/>
                        
                        <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                            <ItemsControl x:Name="ServerSelectionList" Margin="16,0,16,16">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <CheckBox Content="{Binding Name}" 
                                                  IsChecked="{Binding IsSelected}"
                                                  Margin="0,2"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Main Content Area -->
                <Grid Grid.Column="1" Margin="0,8,8,8">
                    <TabControl x:Name="MainTabControl" materialDesign:ColorZoneAssist.Mode="PrimaryMid">
                        <!-- Server Overview Tab -->
                        <TabItem Header="Server Übersicht">
                            <TabItem.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ViewDashboard" Margin="0,0,4,0"/>
                                        <TextBlock Text="Server Übersicht"/>
                                    </StackPanel>
                                </DataTemplate>
                            </TabItem.HeaderTemplate>
                            
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <ItemsControl x:Name="ServerOverviewList" Margin="8">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <materialDesign:Card Style="{StaticResource ServerCard}">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <!-- Status Icon -->
                                                    <materialDesign:PackIcon Grid.Column="0" 
                                                                             Kind="{Binding StatusIcon}" 
                                                                             Foreground="{Binding StatusColor}"
                                                                             Style="{StaticResource StatusIcon}"/>

                                                    <!-- Server Info -->
                                                    <StackPanel Grid.Column="1">
                                                        <TextBlock Text="{Binding Name}" Style="{StaticResource ServerName}"/>
                                                        <TextBlock Text="{Binding Role}" Style="{StaticResource ServerRole}"/>
                                                        <TextBlock Text="{Binding StatusText}" FontSize="12" Margin="8,4,8,0"/>
                                                    </StackPanel>

                                                    <!-- Actions -->
                                                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="Details anzeigen"
                                                                Click="ShowServerDetails_Click"
                                                                Tag="{Binding}">
                                                            <materialDesign:PackIcon Kind="Information"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="Server neu starten"
                                                                Click="RestartSingleServer_Click"
                                                                Tag="{Binding}">
                                                            <materialDesign:PackIcon Kind="Restart"/>
                                                        </Button>
                                                    </StackPanel>
                                                </Grid>
                                            </materialDesign:Card>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </TabItem>

                        <!-- Activity Log Tab -->
                        <TabItem Header="Aktivitätsprotokoll">
                            <TabItem.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileDocument" Margin="0,0,4,0"/>
                                        <TextBlock Text="Aktivitätsprotokoll"/>
                                    </StackPanel>
                                </DataTemplate>
                            </TabItem.HeaderTemplate>
                            
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Log Controls -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="8">
                                    <Button x:Name="ClearLogButton" Content="Log löschen" 
                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Click="ClearLogButton_Click"/>
                                    <Button x:Name="SaveLogButton" Content="Log speichern" 
                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Click="SaveLogButton_Click" Margin="8,0,0,0"/>
                                </StackPanel>

                                <!-- Log Display -->
                                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" 
                                              x:Name="LogScrollViewer">
                                    <TextBox x:Name="LogTextBox" 
                                             IsReadOnly="True" 
                                             TextWrapping="Wrap"
                                             FontFamily="Consolas"
                                             FontSize="11"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             Margin="8"/>
                                </ScrollViewer>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </Grid>

            <!-- Status Bar -->
            <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="StatusText" Grid.Column="0" 
                               Text="Bereit" VerticalAlignment="Center" 
                               Foreground="White"/>

                    <ProgressBar x:Name="ProgressBar" Grid.Column="1" 
                                 Width="200" Height="4" 
                                 Visibility="Collapsed" Margin="8,0"/>

                    <TextBlock x:Name="TimeText" Grid.Column="2" 
                               VerticalAlignment="Center" 
                               Foreground="White"/>
                </Grid>
            </materialDesign:ColorZone>

            <!-- Loading Overlay -->
            <Grid x:Name="LoadingOverlay" Grid.RowSpan="3" 
                  Background="#80000000" Visibility="Collapsed">
                <materialDesign:Card Style="{StaticResource LoadingRing}">
                    <StackPanel>
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                     Value="0" IsIndeterminate="True" 
                                     Width="48" Height="48"/>
                        <TextBlock x:Name="LoadingText" Text="Wird geladen..." 
                                   HorizontalAlignment="Center" Margin="0,16,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</Window>
