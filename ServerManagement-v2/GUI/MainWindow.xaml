<Window x:Class="ServerManagementApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Windows Server Management v2.0" 
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        Background="{StaticResource BackgroundBrush}"
        FontFamily="Segoe UI"
        FontSize="13">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🖥️" FontSize="24" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="Windows Server Management v2.0" 
                               FontSize="20" FontWeight="Bold" 
                               VerticalAlignment="Center" 
                               Foreground="White"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="CredentialsButton" 
                            Style="{StaticResource IconButton}"
                            ToolTip="Anmeldedaten konfigurieren"
                            Click="CredentialsButton_Click">
                        <TextBlock Text="👤" FontSize="16" Foreground="White"/>
                    </Button>
                    <Button x:Name="SettingsButton" 
                            Style="{StaticResource IconButton}"
                            ToolTip="Einstellungen"
                            Click="SettingsButton_Click">
                        <TextBlock Text="⚙️" FontSize="16" Foreground="White"/>
                    </Button>
                    <Button x:Name="RefreshButton" 
                            Style="{StaticResource IconButton}"
                            ToolTip="Aktualisieren"
                            Click="RefreshButton_Click">
                        <TextBlock Text="🔄" FontSize="16" Foreground="White"/>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Sidebar -->
            <Border Grid.Column="0" Background="{StaticResource SurfaceBrush}" 
                    BorderBrush="#E0E0E0" BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <!-- Action Buttons -->
                        <TextBlock Text="AKTIONEN" FontWeight="Bold" Margin="0,0,0,16" 
                                   Foreground="#757575" FontSize="12"/>
                        
                        <Button x:Name="PingAllButton" Style="{StaticResource ActionButton}" 
                                Click="PingAllButton_Click" Margin="0,0,0,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📡" Margin="0,0,8,0"/>
                                <TextBlock Text="Server anpingen"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="SearchUpdatesButton" Style="{StaticResource ActionButton}" 
                                Click="SearchUpdatesButton_Click" Margin="0,0,0,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔍" Margin="0,0,8,0"/>
                                <TextBlock Text="Updates suchen"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="InstallUpdatesButton" Style="{StaticResource ActionButton}" 
                                Click="InstallUpdatesButton_Click" Margin="0,0,0,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📦" Margin="0,0,8,0"/>
                                <TextBlock Text="Updates installieren"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="RestartServersButton" Style="{StaticResource ActionButton}" 
                                Click="RestartServersButton_Click" Margin="0,0,0,8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔄" Margin="0,0,8,0"/>
                                <TextBlock Text="Server neu starten"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="ServerStatusButton" Style="{StaticResource ActionButton}" 
                                Click="ServerStatusButton_Click" Margin="0,0,0,16">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📊" Margin="0,0,8,0"/>
                                <TextBlock Text="Server-Status"/>
                            </StackPanel>
                        </Button>

                        <Rectangle Height="1" Fill="#E0E0E0" Margin="0,0,0,16"/>

                        <!-- Server Selection -->
                        <TextBlock Text="SERVER AUSWAHL" FontWeight="Bold" Margin="0,0,0,16" 
                                   Foreground="#757575" FontSize="12"/>
                        
                        <CheckBox x:Name="SelectAllCheckBox" Content="Alle Server auswählen" 
                                  Margin="0,0,0,16" Checked="SelectAllCheckBox_Checked" 
                                  Unchecked="SelectAllCheckBox_Unchecked"/>
                        
                        <ItemsControl x:Name="ServerSelectionList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Content="{Binding Name}" 
                                              IsChecked="{Binding IsSelected}"
                                              Margin="0,4"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- Main Content Area -->
            <Grid Grid.Column="1" Margin="16">
                <TabControl x:Name="MainTabControl">
                    <!-- Server Overview Tab -->
                    <TabItem Header="📊 Server Übersicht">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <ItemsControl x:Name="ServerOverviewList" Margin="8">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Style="{StaticResource ServerCard}">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- Status Icon -->
                                                <TextBlock Grid.Column="0" 
                                                           Text="{Binding StatusIcon}" 
                                                           FontSize="24"
                                                           Foreground="{Binding StatusColor}"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,16,0"/>

                                                <!-- Server Info -->
                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding Name}" Style="{StaticResource ServerName}"/>
                                                    <TextBlock Text="{Binding Role}" Style="{StaticResource ServerRole}"/>
                                                    <TextBlock Text="{Binding StatusText}" FontSize="12" 
                                                               Margin="8,4,8,0" Foreground="#757575"/>
                                                </StackPanel>

                                                <!-- Actions -->
                                                <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                    <Button Style="{StaticResource IconButton}"
                                                            ToolTip="Details anzeigen"
                                                            Click="ShowServerDetails_Click"
                                                            Tag="{Binding}">
                                                        <TextBlock Text="ℹ️" FontSize="16"/>
                                                    </Button>
                                                    <Button Style="{StaticResource IconButton}"
                                                            ToolTip="Server neu starten"
                                                            Click="RestartSingleServer_Click"
                                                            Tag="{Binding}">
                                                        <TextBlock Text="🔄" FontSize="16"/>
                                                    </Button>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </TabItem>

                    <!-- Activity Log Tab -->
                    <TabItem Header="📋 Aktivitätsprotokoll">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- Log Controls -->
                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                                <Button x:Name="ClearLogButton" Content="Log löschen" 
                                        Style="{StaticResource SecondaryButton}"
                                        Click="ClearLogButton_Click"/>
                                <Button x:Name="SaveLogButton" Content="Log speichern" 
                                        Style="{StaticResource SecondaryButton}"
                                        Click="SaveLogButton_Click" Margin="8,0,0,0"/>
                            </StackPanel>

                            <!-- Log Display -->
                            <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" 
                                    BorderThickness="1" CornerRadius="4">
                                <ScrollViewer x:Name="LogScrollViewer" VerticalScrollBarVisibility="Auto">
                                    <TextBox x:Name="LogTextBox" 
                                             IsReadOnly="True" 
                                             TextWrapping="Wrap"
                                             FontFamily="Consolas"
                                             FontSize="11"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             Margin="8"/>
                                </ScrollViewer>
                            </Border>
                        </Grid>
                    </TabItem>
                </TabControl>
            </Grid>
        </Grid>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#37474F" Padding="16,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="StatusText" Grid.Column="0" 
                           Text="Bereit" VerticalAlignment="Center" 
                           Foreground="White"/>

                <ProgressBar x:Name="ProgressBar" Grid.Column="1" 
                             Width="200" Height="4" 
                             Visibility="Collapsed" Margin="16,0"/>

                <TextBlock x:Name="TimeText" Grid.Column="2" 
                           VerticalAlignment="Center" 
                           Foreground="White"/>
            </Grid>
        </Border>

        <!-- Loading Overlay -->
        <Grid x:Name="LoadingOverlay" Grid.RowSpan="3" 
              Background="#80000000" Visibility="Collapsed">
            <Border Background="White" 
                    CornerRadius="8" 
                    Padding="32"
                    HorizontalAlignment="Center" 
                    VerticalAlignment="Center">
                <StackPanel>
                    <ProgressBar IsIndeterminate="True" 
                                 Width="200" Height="4" 
                                 Margin="0,0,0,16"/>
                    <TextBlock x:Name="LoadingText" Text="Wird geladen..." 
                               HorizontalAlignment="Center" 
                               FontWeight="Medium"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>
