<Application x:Class="ServerManagementApp.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="LightBlue" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Server Status Colors -->
                    <SolidColorBrush x:Key="OnlineBrush" Color="#4CAF50"/>
                    <SolidColorBrush x:Key="OfflineBrush" Color="#F44336"/>
                    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
                    <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
                    
                    <!-- Card Style -->
                    <Style x:Key="ServerCard" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>
                    
                    <!-- Status Icon Style -->
                    <Style x:Key="StatusIcon" TargetType="materialDesign:PackIcon">
                        <Setter Property="Width" Value="24"/>
                        <Setter Property="Height" Value="24"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                    </Style>
                    
                    <!-- Server Name Style -->
                    <Style x:Key="ServerName" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="18"/>
                        <Setter Property="FontWeight" Value="SemiBold"/>
                        <Setter Property="Margin" Value="8,0"/>
                    </Style>
                    
                    <!-- Server Role Style -->
                    <Style x:Key="ServerRole" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="12"/>
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                        <Setter Property="Margin" Value="8,0"/>
                    </Style>
                    
                    <!-- Action Button Style -->
                    <Style x:Key="ActionButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="4"/>
                    </Style>
                    
                    <!-- Progress Ring Style -->
                    <Style x:Key="LoadingRing" TargetType="materialDesign:Card">
                        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
                        <Setter Property="Padding" Value="32"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth3"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
