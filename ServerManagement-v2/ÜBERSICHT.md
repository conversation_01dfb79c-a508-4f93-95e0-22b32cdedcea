# 🚀 Windows Server Management v2.0 - Komplettübersicht

## 📁 Projektstruktur

```
ServerManagement-v2/
├── 🚀 START-ServerManagement.bat     ← PowerShell-Version starten
├── 🖥️ START-GUI.bat                  ← GUI-Version starten
├── 📄 ÜBERSICHT.md                   ← Diese Datei
├── 📖 README.md                      ← PowerShell-Dokumentation
├── 📄 ANLEITUNG.md                   ← Schnellstart-Anleitung
├── ⚙️ Setup-ServerManagement.ps1     ← Einmaliges Setup
├── 🔗 Create-DesktopShortcut.ps1    ← Desktop-Verknüpfung
├── 🔧 ServerManagement.ps1           ← PowerShell-Hauptskript
├── 📂 Config/
│   └── ServerConfig.psd1             ← Server-Konfiguration
├── 📂 Modules/
│   ├── ServerManagement.psm1         ← PowerShell-Kernfunktionen
│   └── ExtendedFeatures.psm1         ← Erweiterte Features
├── 📂 Logs/                          ← Automatische Log-Dateien
├── 📂 Reports/                       ← Generierte Berichte
└── 📂 GUI/                           ← Moderne WPF-Anwendung
    ├── 🖥️ ServerManagementApp.csproj ← .NET-Projekt
    ├── 🎨 MainWindow.xaml             ← Hauptfenster
    ├── 💻 MainWindow.xaml.cs          ← Hauptfenster-Code
    ├── 📊 Models/                     ← Datenmodelle
    ├── 🔧 Services/                   ← PowerShell-Integration
    ├── 🖼️ Views/                      ← UI-Dialoge
    ├── 🔨 build.ps1                   ← Build-Skript
    └── 📖 README-GUI.md               ← GUI-Dokumentation
```

## 🎯 Zwei Versionen verfügbar

### 1. 💻 PowerShell-Version (Konsole)
- **Datei**: `START-ServerManagement.bat`
- **Interface**: Interaktives Konsolen-Menü
- **Vorteile**: Schnell, leichtgewichtig, überall lauffähig
- **Ideal für**: Server-Administratoren, Automatisierung

### 2. 🖥️ GUI-Version (Grafisch)
- **Datei**: `START-GUI.bat`
- **Interface**: Moderne WPF-Anwendung mit Material Design
- **Vorteile**: Intuitiv, übersichtlich, moderne Oberfläche
- **Ideal für**: Benutzer, die grafische Oberflächen bevorzugen

## ⚡ Schnellstart

### PowerShell-Version
```cmd
Doppelklick auf: START-ServerManagement.bat
```

### GUI-Version
```cmd
Doppelklick auf: START-GUI.bat
```

## 🔧 Funktionen (beide Versionen)

### ✅ Kernfunktionen
- **🌐 Server anpingen**: Konnektivitätstest aller Server
- **🔍 Windows Updates suchen**: Parallele Update-Suche
- **📦 Updates installieren**: OHNE automatischen Neustart
- **🔄 Server neu starten**: Intelligente Reihenfolge
- **📊 Server-Status**: Detaillierte Systeminformationen
- **💾 Festplatten-Analyse**: Alle Partitionen automatisch erkennen

### 🚀 Erweiterte Features
- **⚡ Parallele Verarbeitung**: Mehrere Server gleichzeitig
- **🔔 Benachrichtigungen**: Windows-Benachrichtigungen
- **📝 Logging**: Strukturierte Protokollierung
- **🛡️ Sichere Authentifizierung**: Moderne Credential-Verwaltung
- **🎯 Retry-Mechanismen**: Robuste Fehlerbehandlung

## 🎮 Benutzeroberflächen

### PowerShell-Version
```
HAUPTMENÜ
─────────
1. Server anpingen
2. Updates suchen
3. Updates installieren
4. Server neu starten
5. Server-Status
6. Erweiterte Funktionen
7. Einstellungen
8. Berichte
9. Beenden

Auswahl (1-9): _
```

### GUI-Version
```
┌─────────────────────────────────────────────────────────┐
│ [🖥️] Windows Server Management v2.0    [👤][⚙️][🔄] │
├─────────────────┬───────────────────────────────────────┤
│ AKTIONEN        │ Server Übersicht                      │
│ • Server anping │ ┌─────────────────────────────────────┐ │
│ • Updates such  │ │ ✓ DC01    DomainController          │ │
│ • Updates inst  │ │   Online (5ms)                      │ │
│ • Server neust  │ └─────────────────────────────────────┘ │
│ • Server-Status │                                       │
│                 │ [Aktivitätsprotokoll]                 │
│ SERVER AUSWAHL  │                                       │
│ ☑ Alle Server   │                                       │
│ ☑ DC01          │                                       │
│ ☑ DC02          │                                       │
└─────────────────┴───────────────────────────────────────┘
```

## ⚙️ Konfiguration

### Server-Liste anpassen
Bearbeiten Sie `Config\ServerConfig.psd1`:

```powershell
ServerList = @(
    @{
        Name = "DC01"
        Role = "DomainController"
        Priority = 1
        RestartDelay = 180  # 3 Minuten
    },
    @{
        Name = "DC02"
        Role = "DomainController"
        Priority = 2
        RestartDelay = 120  # 2 Minuten
    },
    # ... weitere Server
)
```

### Neustart-Reihenfolge
Die Server werden automatisch in der korrekten Reihenfolge neu gestartet:
1. **DC01** → 3 Min Wartezeit
2. **DC02** → 2 Min Wartezeit
3. **DB01** → 3 Min Wartezeit
4. **APP01** → 2 Min Wartezeit
5. **MAIL** → 2 Min Wartezeit
6. **FILE** → 1.5 Min Wartezeit
7. **TS01** → Fertig

## 🛠️ Installation & Setup

### Automatisches Setup
```powershell
# Führen Sie einmalig aus:
.\Setup-ServerManagement.ps1
```

### Manuelle Installation

#### PowerShell-Version
- Keine Installation erforderlich
- Benötigt PowerShell 7+

#### GUI-Version
```powershell
# .NET 8 installieren
winget install Microsoft.DotNet.SDK.8

# GUI kompilieren
cd GUI
.\build.ps1 -Publish
```

## 📋 Systemanforderungen

### Minimum
- **Windows 11** oder Windows 10
- **PowerShell 7+**
- **WinRM** aktiviert auf Zielservern
- **Administratorrechte** für Remote-Zugriff

### Für GUI-Version zusätzlich
- **.NET 8 Runtime** (oder SDK)
- **WPF-Unterstützung** (Windows Desktop)

## 🔐 Sicherheit

### Authentifizierung
- **Keine Admin-Rechte** für das Skript selbst erforderlich
- **Credential-Abfrage** für Remote-Zugriff
- **Sichere Speicherung** nur im Arbeitsspeicher
- **Moderne Kerberos-Authentifizierung**

### Best Practices
- Verwenden Sie dedizierte Service-Accounts
- Aktivieren Sie WinRM nur auf notwendigen Servern
- Überwachen Sie die Log-Dateien regelmäßig

## 📊 Monitoring & Logging

### Log-Dateien
- **PowerShell**: `Logs\ServerManagement_YYYYMMDD.log`
- **GUI**: Aktivitätsprotokoll in der Anwendung
- **Aufbewahrung**: 30 Tage (konfigurierbar)

### Berichte
- **JSON-Format**: Strukturierte Daten
- **HTML-Format**: Lesbare Berichte
- **Automatische Generierung**: Nach wichtigen Aktionen

## 🆘 Troubleshooting

### Häufige Probleme

#### PowerShell 7 nicht gefunden
```powershell
winget install Microsoft.PowerShell
```

#### WinRM nicht konfiguriert
```powershell
# Auf jedem Zielserver:
Enable-PSRemoting -Force
```

#### .NET 8 nicht gefunden (GUI)
```powershell
winget install Microsoft.DotNet.SDK.8
```

#### Server nicht erreichbar
1. Ping-Test: `ping servername`
2. WinRM-Test: `Test-WSMan servername`
3. Firewall prüfen
4. Credentials überprüfen

## 🎯 Welche Version wählen?

### PowerShell-Version wählen wenn:
- ✅ Sie Konsolen-Interfaces bevorzugen
- ✅ Sie das Skript automatisieren möchten
- ✅ Sie minimale Systemanforderungen haben
- ✅ Sie schnelle Ausführung benötigen

### GUI-Version wählen wenn:
- ✅ Sie grafische Oberflächen bevorzugen
- ✅ Sie mehrere Aktionen parallel ausführen
- ✅ Sie eine moderne, intuitive Bedienung wünschen
- ✅ Sie Echtzeit-Updates der Server-Status benötigen

## 🚀 Erste Schritte

1. **Konfiguration anpassen**:
   ```powershell
   notepad Config\ServerConfig.psd1
   ```

2. **Version wählen und starten**:
   - PowerShell: `START-ServerManagement.bat`
   - GUI: `START-GUI.bat`

3. **Anmeldedaten eingeben**:
   - Standard-Benutzer: `edv`
   - Ihr Administrator-Passwort

4. **Server testen**:
   - Starten Sie mit einem Ping-Test
   - Prüfen Sie den Server-Status

5. **Aktionen ausführen**:
   - Updates suchen und installieren
   - Server bei Bedarf neu starten

## 📞 Support

Bei Fragen oder Problemen:
1. Prüfen Sie die Log-Dateien
2. Testen Sie die Netzwerkverbindung
3. Überprüfen Sie die WinRM-Konfiguration
4. Kontaktieren Sie den System-Administrator

---

**🎉 Viel Erfolg mit dem Windows Server Management System v2.0!**

**Entwickelt von Tonino Gerns - 2025**
