@{
    # Server-Konfiguration für Windows Server 2022 Umgebung
    # Erstellt: $(Get-Date -Format 'yyyy-MM-dd')
    
    # Server-Liste in der korrekten Neustart-Reihenfolge
    ServerList = @(
        @{
            Name = "DC01"
            Role = "DomainController"
            Priority = 1
            RestartDelay = 180  # 3 Minuten warten nach Neustart
        },
        @{
            Name = "DC02" 
            Role = "DomainController"
            Priority = 2
            RestartDelay = 120  # 2 Minuten warten nach Neustart
        },
        @{
            Name = "DB01"
            Role = "DatabaseServer"
            Priority = 3
            RestartDelay = 180  # 3 Minuten warten nach Neustart
        },
        @{
            Name = "APP01"
            Role = "ApplicationServer"
            Priority = 4
            RestartDelay = 120  # 2 Minuten warten nach Neustart
        },
        @{
            Name = "MAIL"
            Role = "MailServer"
            Priority = 5
            RestartDelay = 120  # 2 Minuten warten nach Neustart
        },
        @{
            Name = "FILE"
            Role = "FileServer"
            Priority = 6
            RestartDelay = 90   # 1.5 Minuten warten nach Neustart
        },
        @{
            Name = "TS01"
            Role = "TerminalServer"
            Priority = 7
            RestartDelay = 0    # Letzter Server, kein Warten erforderlich
        }
    )
    
    # Allgemeine Einstellungen
    Settings = @{
        # Timeout-Werte in Sekunden
        PingTimeout = 5
        ConnectionTimeout = 30
        UpdateSearchTimeout = 300
        UpdateInstallTimeout = 1800
        RestartTimeout = 600
        
        # Retry-Einstellungen
        MaxRetries = 3
        RetryDelay = 10
        
        # Logging
        LogPath = "C:\Logs\ServerManagement"
        LogRetentionDays = 30
        
        # Benachrichtigungen
        EnableToastNotifications = $true
        EnableEmailNotifications = $false
        
        # E-Mail-Einstellungen (falls aktiviert)
        SMTPServer = "mail.company.local"
        SMTPPort = 587
        FromEmail = "<EMAIL>"
        ToEmail = @("<EMAIL>")
        
        # Teams Webhook (optional)
        TeamsWebhookUrl = ""
        
        # Windows Update Einstellungen
        UpdateCategories = @(
            "Security Updates",
            "Critical Updates", 
            "Update Rollups",
            "Definition Updates"
        )
        ExcludeDriverUpdates = $true
        AutoRebootAfterUpdates = $false
        
        # Monitoring
        CheckDiskSpace = $true
        DiskSpaceThreshold = 15  # Warnung bei weniger als 15% freiem Speicher
        CheckServices = $true
        CriticalServices = @(
            "Spooler",
            "DHCP Client", 
            "DNS Client",
            "Windows Time",
            "Remote Desktop Services"
        )
    }
    
    # Credentials (werden zur Laufzeit abgefragt)
    Credentials = @{
        DefaultUsername = "edv"
        UseCurrentUser = $false
        SaveCredentials = $false  # Aus Sicherheitsgründen nicht speichern
    }
    
    # Erweiterte Features
    Features = @{
        ParallelProcessing = $true
        MaxParallelJobs = 4
        ShowProgressBars = $true
        DetailedLogging = $true
        ExportReports = $true
        ReportFormat = "JSON"  # JSON, CSV, HTML
    }
}
