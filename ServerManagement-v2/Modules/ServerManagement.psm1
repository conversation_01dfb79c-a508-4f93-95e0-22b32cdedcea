#Requires -Version 7.0
<#
.SYNOPSIS
    Modernes PowerShell-Modul für Windows Server 2022 Verwaltung
.DESCRIPTION
    Dieses Modul bietet moderne, zuverlässige Funktionen für die Verwaltung von Windows Server 2022
    von einem Windows 11 Client aus.
.AUTHOR
    Tonino Gerns
.VERSION
    2.0.0
#>

# Globale Variablen
$script:Config = $null
$script:Credentials = $null
$script:LogPath = $null

#region Logging Functions

function Write-ServerLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Message,
        
        [ValidateSet('Info', 'Warning', 'Error', 'Success', 'Debug')]
        [string]$Level = 'Info',
        
        [string]$ServerName = 'LOCAL',
        
        [string]$Component = 'General'
    )
    
    if (-not $script:LogPath) {
        $script:LogPath = Join-Path $env:TEMP "ServerManagement_$(Get-Date -Format 'yyyyMMdd').log"
    }
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $logEntry = "[$timestamp] [$Level] [$ServerName] [$Component] $Message"
    
    # Konsolen-Ausgabe mit Farben
    $color = switch ($Level) {
        'Info' { 'White' }
        'Warning' { 'Yellow' }
        'Error' { 'Red' }
        'Success' { 'Green' }
        'Debug' { 'Gray' }
    }
    
    Write-Host $logEntry -ForegroundColor $color
    
    # In Datei schreiben
    try {
        $logEntry | Out-File -FilePath $script:LogPath -Append -Encoding UTF8
    }
    catch {
        Write-Warning "Fehler beim Schreiben in Log-Datei: $_"
    }
}

function Initialize-Logging {
    [CmdletBinding()]
    param([string]$LogDirectory)
    
    if ($LogDirectory -and (Test-Path $LogDirectory)) {
        $script:LogPath = Join-Path $LogDirectory "ServerManagement_$(Get-Date -Format 'yyyyMMdd').log"
    } else {
        $script:LogPath = Join-Path $env:TEMP "ServerManagement_$(Get-Date -Format 'yyyyMMdd').log"
    }
    
    # Log-Verzeichnis erstellen falls nicht vorhanden
    $logDir = Split-Path $script:LogPath -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -Path $logDir -ItemType Directory -Force | Out-Null
    }
    
    Write-ServerLog "Logging initialisiert: $script:LogPath" -Level Success -Component "Logging"
}

#endregion

#region Configuration Functions

function Import-ServerConfig {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ConfigPath
    )
    
    try {
        if (-not (Test-Path $ConfigPath)) {
            throw "Konfigurationsdatei nicht gefunden: $ConfigPath"
        }
        
        $script:Config = Import-PowerShellDataFile -Path $ConfigPath
        Write-ServerLog "Konfiguration erfolgreich geladen" -Level Success -Component "Config"
        
        # Logging initialisieren
        if ($script:Config.Settings.LogPath) {
            Initialize-Logging -LogDirectory $script:Config.Settings.LogPath
        }
        
        return $script:Config
    }
    catch {
        Write-ServerLog "Fehler beim Laden der Konfiguration: $_" -Level Error -Component "Config"
        throw
    }
}

function Get-ServerCredentials {
    [CmdletBinding()]
    param()
    
    if ($script:Credentials) {
        return $script:Credentials
    }
    
    $username = $script:Config.Credentials.DefaultUsername
    if (-not $username) {
        $username = Read-Host "Benutzername eingeben"
    }
    
    Write-Host "Anmeldedaten für Benutzer: $username" -ForegroundColor Cyan
    $password = Read-Host "Passwort eingeben" -AsSecureString
    
    $script:Credentials = New-Object PSCredential($username, $password)
    Write-ServerLog "Anmeldedaten erfolgreich erstellt für Benutzer: $username" -Level Success -Component "Auth"
    
    return $script:Credentials
}

#endregion

#region Network Functions

function Test-ServerConnectivity {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,
        
        [int]$Timeout = 5
    )
    
    try {
        Write-ServerLog "Teste Verbindung zu $ServerName" -Level Info -ServerName $ServerName -Component "Network"
        
        $ping = Test-Connection -ComputerName $ServerName -Count 1 -TimeoutSeconds $Timeout -ErrorAction Stop
        
        $result = [PSCustomObject]@{
            ServerName = $ServerName
            IsOnline = $true
            ResponseTime = $ping.ResponseTime
            IPAddress = $ping.Address
            Timestamp = Get-Date
        }
        
        Write-ServerLog "Server $ServerName ist erreichbar (${$ping.ResponseTime}ms)" -Level Success -ServerName $ServerName -Component "Network"
        return $result
    }
    catch {
        $result = [PSCustomObject]@{
            ServerName = $ServerName
            IsOnline = $false
            ResponseTime = $null
            IPAddress = $null
            Error = $_.Exception.Message
            Timestamp = Get-Date
        }
        
        Write-ServerLog "Server $ServerName ist nicht erreichbar: $_" -Level Warning -ServerName $ServerName -Component "Network"
        return $result
    }
}

function Test-AllServers {
    [CmdletBinding()]
    param()
    
    if (-not $script:Config) {
        throw "Konfiguration nicht geladen. Verwenden Sie Import-ServerConfig zuerst."
    }
    
    Write-ServerLog "Starte Konnektivitätstest für alle Server" -Level Info -Component "Network"
    
    $results = @()
    $serverList = $script:Config.ServerList
    
    if ($script:Config.Features.ParallelProcessing) {
        Write-ServerLog "Verwende parallele Verarbeitung" -Level Info -Component "Network"
        
        $results = $serverList | ForEach-Object -Parallel {
            $server = $_
            $timeout = $using:script:Config.Settings.PingTimeout

            # Import des Moduls in jedem parallelen Job
            $modulePath = Join-Path (Split-Path $using:PSScriptRoot -Parent) "Modules\ServerManagement.psm1"
            Import-Module $modulePath -Force

            Test-ServerConnectivity -ServerName $server.Name -Timeout $timeout
        } -ThrottleLimit $script:Config.Features.MaxParallelJobs
    } else {
        foreach ($server in $serverList) {
            $results += Test-ServerConnectivity -ServerName $server.Name -Timeout $script:Config.Settings.PingTimeout
        }
    }
    
    # Ergebnisse anzeigen
    $results | Format-Table -AutoSize ServerName, IsOnline, ResponseTime, IPAddress
    
    $onlineCount = ($results | Where-Object IsOnline).Count
    $totalCount = $results.Count
    
    Write-ServerLog "Konnektivitätstest abgeschlossen: $onlineCount/$totalCount Server online" -Level Success -Component "Network"
    
    return $results
}

#endregion

#region Windows Update Functions

function Search-WindowsUpdatesModern {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,
        
        [PSCredential]$Credential
    )
    
    Write-ServerLog "Suche nach Windows Updates auf $ServerName" -Level Info -ServerName $ServerName -Component "Updates"
    
    try {
        $scriptBlock = {
            # Moderne Windows Update Suche mit COM-Objekten
            $updateSession = New-Object -ComObject Microsoft.Update.Session
            $updateSearcher = $updateSession.CreateUpdateSearcher()
            
            # Suche nach Updates
            $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Software'")
            
            $updates = @()
            foreach ($update in $searchResult.Updates) {
                $updates += [PSCustomObject]@{
                    Title = $update.Title
                    Description = $update.Description
                    Size = [math]::Round($update.MaxDownloadSize / 1MB, 2)
                    IsDownloaded = $update.IsDownloaded
                    Categories = ($update.Categories | ForEach-Object { $_.Name }) -join ", "
                    KBArticleIDs = $update.KBArticleIDs -join ", "
                    SecurityBulletinIDs = $update.SecurityBulletinIDs -join ", "
                    RebootRequired = $update.RebootRequired
                }
            }
            
            return $updates
        }
        
        $updates = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock $scriptBlock -ErrorAction Stop
        
        Write-ServerLog "Gefunden: $($updates.Count) Updates auf $ServerName" -Level Success -ServerName $ServerName -Component "Updates"
        
        return $updates
    }
    catch {
        Write-ServerLog "Fehler bei Update-Suche auf ${ServerName}: $_" -Level Error -ServerName $ServerName -Component "Updates"
        throw
    }
}

function Install-WindowsUpdatesModern {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,

        [PSCredential]$Credential,

        [switch]$NoReboot
    )

    Write-ServerLog "Installiere Windows Updates auf $ServerName" -Level Info -ServerName $ServerName -Component "Updates"

    try {
        $scriptBlock = {
            param($NoReboot)

            $updateSession = New-Object -ComObject Microsoft.Update.Session
            $updateSearcher = $updateSession.CreateUpdateSearcher()

            # Suche nach Updates
            $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Software'")

            if ($searchResult.Updates.Count -eq 0) {
                return @{ Status = "NoUpdates"; Message = "Keine Updates verfügbar" }
            }

            # Updates herunterladen
            $updatesToDownload = New-Object -ComObject Microsoft.Update.UpdateColl
            foreach ($update in $searchResult.Updates) {
                $updatesToDownload.Add($update) | Out-Null
            }

            $downloader = $updateSession.CreateUpdateDownloader()
            $downloader.Updates = $updatesToDownload
            $downloadResult = $downloader.Download()

            if ($downloadResult.ResultCode -ne 2) {
                throw "Download fehlgeschlagen: $($downloadResult.ResultCode)"
            }

            # Updates installieren
            $updatesToInstall = New-Object -ComObject Microsoft.Update.UpdateColl
            foreach ($update in $searchResult.Updates) {
                if ($update.IsDownloaded) {
                    $updatesToInstall.Add($update) | Out-Null
                }
            }

            $installer = $updateSession.CreateUpdateInstaller()
            $installer.Updates = $updatesToInstall

            if ($NoReboot) {
                $installer.AllowSourcePrompts = $false
            }

            $installResult = $installer.Install()

            return @{
                Status = "Success"
                ResultCode = $installResult.ResultCode
                RebootRequired = $installResult.RebootRequired
                UpdatesInstalled = $updatesToInstall.Count
            }
        }

        $result = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $NoReboot.IsPresent -ErrorAction Stop

        Write-ServerLog "Updates installiert auf ${ServerName}: $($result.UpdatesInstalled) Updates, Neustart erforderlich: $($result.RebootRequired)" -Level Success -ServerName $ServerName -Component "Updates"

        return $result
    }
    catch {
        Write-ServerLog "Fehler bei Update-Installation auf ${ServerName}: $_" -Level Error -ServerName $ServerName -Component "Updates"
        throw
    }
}

function Restart-ServerSafe {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,

        [PSCredential]$Credential,

        [int]$TimeoutSeconds = 600
    )

    Write-ServerLog "Starte Neustart von $ServerName" -Level Info -ServerName $ServerName -Component "Restart"

    try {
        # Neustart initiieren
        Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock {
            Restart-Computer -Force
        } -ErrorAction Stop

        Write-ServerLog "Neustart-Befehl gesendet an $ServerName" -Level Info -ServerName $ServerName -Component "Restart"

        # Warten bis Server offline ist
        $offline = $false
        $attempts = 0
        while (-not $offline -and $attempts -lt 30) {
            Start-Sleep -Seconds 10
            $attempts++
            try {
                Test-Connection -ComputerName $ServerName -Count 1 -TimeoutSeconds 5 -ErrorAction Stop | Out-Null
            }
            catch {
                $offline = $true
                Write-ServerLog "Server $ServerName ist offline" -Level Info -ServerName $ServerName -Component "Restart"
            }
        }

        # Warten bis Server wieder online ist
        $online = $false
        $startTime = Get-Date
        while (-not $online -and ((Get-Date) - $startTime).TotalSeconds -lt $TimeoutSeconds) {
            Start-Sleep -Seconds 15
            try {
                Test-Connection -ComputerName $ServerName -Count 1 -TimeoutSeconds 5 -ErrorAction Stop | Out-Null
                $online = $true
                Write-ServerLog "Server $ServerName ist wieder online" -Level Success -ServerName $ServerName -Component "Restart"
            }
            catch {
                Write-Host "." -NoNewline
            }
        }

        if (-not $online) {
            throw "Server $ServerName ist nach $TimeoutSeconds Sekunden nicht wieder online gekommen"
        }

        # Zusätzliche Zeit für Services
        Write-ServerLog "Warte auf vollständige Initialisierung von $ServerName" -Level Info -ServerName $ServerName -Component "Restart"
        Start-Sleep -Seconds 30

        return @{
            ServerName = $ServerName
            RestartSuccessful = $true
            TotalTime = ((Get-Date) - $startTime).TotalSeconds
        }
    }
    catch {
        Write-ServerLog "Fehler beim Neustart von ${ServerName}: $_" -Level Error -ServerName $ServerName -Component "Restart"
        return @{
            ServerName = $ServerName
            RestartSuccessful = $false
            Error = $_.Exception.Message
        }
    }
}

function Restart-AllServersOrdered {
    [CmdletBinding()]
    param(
        [PSCredential]$Credential
    )

    if (-not $script:Config) {
        throw "Konfiguration nicht geladen. Verwenden Sie Import-ServerConfig zuerst."
    }

    Write-ServerLog "Starte geordneten Neustart aller Server" -Level Info -Component "Restart"

    $results = @()
    $serverList = $script:Config.ServerList | Sort-Object Priority

    foreach ($server in $serverList) {
        Write-ServerLog "Starte Neustart von $($server.Name) (Priorität: $($server.Priority))" -Level Info -ServerName $server.Name -Component "Restart"

        $result = Restart-ServerSafe -ServerName $server.Name -Credential $Credential -TimeoutSeconds $script:Config.Settings.RestartTimeout
        $results += $result

        if ($result.RestartSuccessful -and $server.RestartDelay -gt 0) {
            Write-ServerLog "Warte $($server.RestartDelay) Sekunden vor dem nächsten Server" -Level Info -Component "Restart"
            Start-Sleep -Seconds $server.RestartDelay
        }
    }

    # Zusammenfassung
    $successful = ($results | Where-Object RestartSuccessful).Count
    $total = $results.Count

    Write-ServerLog "Neustart-Vorgang abgeschlossen: $successful/$total Server erfolgreich" -Level Success -Component "Restart"

    return $results
}

#endregion

#region Notification Functions

function Send-ToastNotification {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Title,

        [Parameter(Mandatory)]
        [string]$Message,

        [ValidateSet('Info', 'Warning', 'Error', 'Success')]
        [string]$Type = 'Info'
    )

    if (-not $script:Config.Settings.EnableToastNotifications) {
        return
    }

    try {
        # Windows 11 Toast Notification
        $xml = @"
<toast>
    <visual>
        <binding template="ToastGeneric">
            <text>$Title</text>
            <text>$Message</text>
        </binding>
    </visual>
</toast>
"@

        $XmlDocument = [Windows.Data.Xml.Dom.XmlDocument, Windows.Data.Xml.Dom.XmlDocument, ContentType = WindowsRuntime]::New()
        $XmlDocument.LoadXml($xml)

        $AppId = "Microsoft.Windows.Computer"
        [Windows.UI.Notifications.ToastNotificationManager, Windows.UI.Notifications, ContentType = WindowsRuntime]::CreateToastNotifier($AppId).Show($XmlDocument)

        Write-ServerLog "Toast-Benachrichtigung gesendet: $Title" -Level Info -Component "Notification"
    }
    catch {
        Write-ServerLog "Fehler beim Senden der Toast-Benachrichtigung: $_" -Level Warning -Component "Notification"
    }
}

#endregion

# Exportiere die öffentlichen Funktionen
Export-ModuleMember -Function @(
    'Write-ServerLog',
    'Initialize-Logging',
    'Import-ServerConfig',
    'Get-ServerCredentials',
    'Test-ServerConnectivity',
    'Test-AllServers',
    'Search-WindowsUpdatesModern',
    'Install-WindowsUpdatesModern',
    'Restart-ServerSafe',
    'Restart-AllServersOrdered',
    'Send-ToastNotification'
)
