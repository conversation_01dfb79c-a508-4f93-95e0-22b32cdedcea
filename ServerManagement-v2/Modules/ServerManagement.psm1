#Requires -Version 7.0
<#
.SYNOPSIS
    Modernes PowerShell-Modul für Windows Server 2022 Verwaltung
.DESCRIPTION
    Dieses Modul bietet moderne, zuverlässige Funktionen für die Verwaltung von Windows Server 2022
    von einem Windows 11 Client aus.
.AUTHOR
    Tonino Gerns
.VERSION
    2.0.0
#>

# Globale Variablen
$script:Config = $null
$script:Credentials = $null
$script:LogPath = $null

#region Logging Functions

function Write-ServerLog {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Message,

        [ValidateSet('Info', 'Warning', 'Error', 'Success', 'Debug')]
        [string]$Level = 'Info',

        [string]$ServerName = 'LOCAL',

        [string]$Component = 'General'
    )

    if (-not $script:LogPath) {
        $script:LogPath = Join-Path $env:TEMP "ServerManagement_$(Get-Date -Format 'yyyyMMdd').log"
    }

    $timestamp = Get-Date -Format 'HH:mm:ss'
    $logEntry = "[$timestamp] [$Level] [$ServerName] [$Component] $Message"

    # Schlichte Konsolen-Ausgabe
    $prefix = switch ($Level) {
        'Info' { '  ' }
        'Warning' { '⚠ ' }
        'Error' { '✗ ' }
        'Success' { '✓ ' }
        'Debug' { '  ' }
    }

    $color = switch ($Level) {
        'Info' { 'Gray' }
        'Warning' { 'Yellow' }
        'Error' { 'Red' }
        'Success' { 'Green' }
        'Debug' { 'DarkGray' }
    }

    # Nur bei wichtigen Meldungen ausgeben
    if ($Level -ne 'Debug') {
        Write-Host "$prefix$Message" -ForegroundColor $color
    }
    
    # In Datei schreiben (mit Retry für parallele Zugriffe)
    $retryCount = 0
    $maxRetries = 3
    do {
        try {
            $logEntry | Out-File -FilePath $script:LogPath -Append -Encoding UTF8 -ErrorAction Stop
            break
        }
        catch {
            $retryCount++
            if ($retryCount -ge $maxRetries) {
                # Nur bei wiederholten Fehlern warnen, nicht bei jedem parallelen Zugriff
                if ($retryCount -eq $maxRetries) {
                    Write-Warning "Log-Datei nach $maxRetries Versuchen nicht beschreibbar"
                }
                break
            }
            Start-Sleep -Milliseconds (Get-Random -Minimum 10 -Maximum 50)
        }
    } while ($retryCount -lt $maxRetries)
}

function Initialize-Logging {
    [CmdletBinding()]
    param([string]$LogDirectory)
    
    if ($LogDirectory -and (Test-Path $LogDirectory)) {
        $script:LogPath = Join-Path $LogDirectory "ServerManagement_$(Get-Date -Format 'yyyyMMdd').log"
    } else {
        $script:LogPath = Join-Path $env:TEMP "ServerManagement_$(Get-Date -Format 'yyyyMMdd').log"
    }
    
    # Log-Verzeichnis erstellen falls nicht vorhanden
    $logDir = Split-Path $script:LogPath -Parent
    if (-not (Test-Path $logDir)) {
        New-Item -Path $logDir -ItemType Directory -Force | Out-Null
    }
    
    Write-ServerLog "Logging initialisiert: $script:LogPath" -Level Success -Component "Logging"
}

#endregion

#region Configuration Functions

function Import-ServerConfig {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ConfigPath
    )
    
    try {
        if (-not (Test-Path $ConfigPath)) {
            throw "Konfigurationsdatei nicht gefunden: $ConfigPath"
        }
        
        $script:Config = Import-PowerShellDataFile -Path $ConfigPath
        Write-ServerLog "Konfiguration erfolgreich geladen" -Level Success -Component "Config"
        
        # Logging initialisieren
        if ($script:Config.Settings.LogPath) {
            Initialize-Logging -LogDirectory $script:Config.Settings.LogPath
        }
        
        return $script:Config
    }
    catch {
        Write-ServerLog "Fehler beim Laden der Konfiguration: $_" -Level Error -Component "Config"
        throw
    }
}

function Get-ServerCredentials {
    [CmdletBinding()]
    param()

    if ($script:Credentials) {
        return $script:Credentials
    }

    $username = $script:Config.Credentials.DefaultUsername
    if (-not $username) {
        $username = Read-Host "Benutzername"
    }

    Write-Host ""
    Write-Host "ANMELDEDATEN" -ForegroundColor White
    Write-Host "────────────" -ForegroundColor Gray
    Write-Host "Benutzer: $username" -ForegroundColor Gray
    $password = Read-Host "Passwort" -AsSecureString

    $script:Credentials = New-Object PSCredential($username, $password)
    Write-Host "✓ Anmeldedaten gespeichert" -ForegroundColor Green

    return $script:Credentials
}

#endregion

#region Network Functions

function Test-ServerConnectivity {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,

        [int]$Timeout = 5
    )

    try {
        $ping = Test-Connection -ComputerName $ServerName -Count 1 -TimeoutSeconds $Timeout -ErrorAction Stop

        $result = [PSCustomObject]@{
            ServerName = $ServerName
            IsOnline = $true
            ResponseTime = $ping.ResponseTime
            IPAddress = $ping.Address
            Timestamp = Get-Date
        }

        return $result
    }
    catch {
        $result = [PSCustomObject]@{
            ServerName = $ServerName
            IsOnline = $false
            ResponseTime = $null
            IPAddress = $null
            Error = $_.Exception.Message
            Timestamp = Get-Date
        }

        return $result
    }
}

function Test-AllServers {
    [CmdletBinding()]
    param()

    if (-not $script:Config) {
        throw "Konfiguration nicht geladen. Verwenden Sie Import-ServerConfig zuerst."
    }

    $results = @()
    $serverList = $script:Config.ServerList

    if ($script:Config.Features.ParallelProcessing) {
        $results = $serverList | ForEach-Object -Parallel {
            $server = $_
            $timeout = $using:script:Config.Settings.PingTimeout

            # Import des Moduls in jedem parallelen Job
            $modulePath = Join-Path (Split-Path $using:PSScriptRoot -Parent) "Modules\ServerManagement.psm1"
            Import-Module $modulePath -Force

            Test-ServerConnectivity -ServerName $server.Name -Timeout $timeout
        } -ThrottleLimit $script:Config.Features.MaxParallelJobs
    } else {
        foreach ($server in $serverList) {
            $results += Test-ServerConnectivity -ServerName $server.Name -Timeout $script:Config.Settings.PingTimeout
        }
    }

    # Schlichte Ergebnisanzeige
    Write-Host ""
    foreach ($result in $results) {
        if ($result.IsOnline) {
            Write-Host "✓ $($result.ServerName.PadRight(8)) Online  ($($result.ResponseTime)ms)" -ForegroundColor Green
        } else {
            Write-Host "✗ $($result.ServerName.PadRight(8)) Offline" -ForegroundColor Red
        }
    }

    $onlineCount = ($results | Where-Object IsOnline).Count
    $totalCount = $results.Count
    Write-Host ""
    Write-Host "  $onlineCount von $totalCount Servern online" -ForegroundColor Gray

    return $results
}

#endregion

#region Windows Update Functions

function Search-WindowsUpdatesModern {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,
        
        [PSCredential]$Credential
    )
    
    Write-ServerLog "Suche nach Windows Updates auf $ServerName" -Level Info -ServerName $ServerName -Component "Updates"
    
    try {
        $scriptBlock = {
            # Moderne Windows Update Suche mit COM-Objekten
            $updateSession = New-Object -ComObject Microsoft.Update.Session
            $updateSearcher = $updateSession.CreateUpdateSearcher()
            
            # Suche nach Updates
            $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Software'")
            
            $updates = @()
            foreach ($update in $searchResult.Updates) {
                $updates += [PSCustomObject]@{
                    Title = $update.Title
                    Description = $update.Description
                    Size = [math]::Round($update.MaxDownloadSize / 1MB, 2)
                    IsDownloaded = $update.IsDownloaded
                    Categories = ($update.Categories | ForEach-Object { $_.Name }) -join ", "
                    KBArticleIDs = $update.KBArticleIDs -join ", "
                    SecurityBulletinIDs = $update.SecurityBulletinIDs -join ", "
                    RebootRequired = $update.RebootRequired
                }
            }
            
            return $updates
        }
        
        $updates = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock $scriptBlock -ErrorAction Stop
        
        Write-ServerLog "Gefunden: $($updates.Count) Updates auf $ServerName" -Level Success -ServerName $ServerName -Component "Updates"
        
        return $updates
    }
    catch {
        Write-ServerLog "Fehler bei Update-Suche auf ${ServerName}: $_" -Level Error -ServerName $ServerName -Component "Updates"
        throw
    }
}

function Install-WindowsUpdatesModern {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,

        [PSCredential]$Credential,

        [switch]$NoReboot
    )

    Write-ServerLog "Installiere Windows Updates auf $ServerName" -Level Info -ServerName $ServerName -Component "Updates"

    try {
        $scriptBlock = {
            param($NoReboot)

            $updateSession = New-Object -ComObject Microsoft.Update.Session
            $updateSearcher = $updateSession.CreateUpdateSearcher()

            # Suche nach Updates
            $searchResult = $updateSearcher.Search("IsInstalled=0 and Type='Software'")

            if ($searchResult.Updates.Count -eq 0) {
                return @{ Status = "NoUpdates"; Message = "Keine Updates verfügbar" }
            }

            # Updates herunterladen
            $updatesToDownload = New-Object -ComObject Microsoft.Update.UpdateColl
            foreach ($update in $searchResult.Updates) {
                $updatesToDownload.Add($update) | Out-Null
            }

            $downloader = $updateSession.CreateUpdateDownloader()
            $downloader.Updates = $updatesToDownload
            $downloadResult = $downloader.Download()

            if ($downloadResult.ResultCode -ne 2) {
                throw "Download fehlgeschlagen: $($downloadResult.ResultCode)"
            }

            # Updates installieren
            $updatesToInstall = New-Object -ComObject Microsoft.Update.UpdateColl
            foreach ($update in $searchResult.Updates) {
                if ($update.IsDownloaded) {
                    $updatesToInstall.Add($update) | Out-Null
                }
            }

            $installer = $updateSession.CreateUpdateInstaller()
            $installer.Updates = $updatesToInstall

            if ($NoReboot) {
                $installer.AllowSourcePrompts = $false
            }

            $installResult = $installer.Install()

            return @{
                Status = "Success"
                ResultCode = $installResult.ResultCode
                RebootRequired = $installResult.RebootRequired
                UpdatesInstalled = $updatesToInstall.Count
            }
        }

        $result = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock $scriptBlock -ArgumentList $NoReboot.IsPresent -ErrorAction Stop

        Write-ServerLog "Updates installiert auf ${ServerName}: $($result.UpdatesInstalled) Updates, Neustart erforderlich: $($result.RebootRequired)" -Level Success -ServerName $ServerName -Component "Updates"

        return $result
    }
    catch {
        Write-ServerLog "Fehler bei Update-Installation auf ${ServerName}: $_" -Level Error -ServerName $ServerName -Component "Updates"
        throw
    }
}

function Restart-ServerSafe {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,

        [PSCredential]$Credential,

        [int]$TimeoutSeconds = 600
    )

    Write-ServerLog "Starte Neustart von $ServerName" -Level Info -ServerName $ServerName -Component "Restart"

    try {
        # Neustart initiieren
        Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock {
            Restart-Computer -Force
        } -ErrorAction Stop

        Write-ServerLog "Neustart-Befehl gesendet an $ServerName" -Level Info -ServerName $ServerName -Component "Restart"

        # Warten bis Server offline ist
        $offline = $false
        $attempts = 0
        while (-not $offline -and $attempts -lt 30) {
            Start-Sleep -Seconds 10
            $attempts++
            try {
                Test-Connection -ComputerName $ServerName -Count 1 -TimeoutSeconds 5 -ErrorAction Stop | Out-Null
            }
            catch {
                $offline = $true
                Write-ServerLog "Server $ServerName ist offline" -Level Info -ServerName $ServerName -Component "Restart"
            }
        }

        # Warten bis Server wieder online ist
        $online = $false
        $startTime = Get-Date
        while (-not $online -and ((Get-Date) - $startTime).TotalSeconds -lt $TimeoutSeconds) {
            Start-Sleep -Seconds 15
            try {
                Test-Connection -ComputerName $ServerName -Count 1 -TimeoutSeconds 5 -ErrorAction Stop | Out-Null
                $online = $true
                Write-ServerLog "Server $ServerName ist wieder online" -Level Success -ServerName $ServerName -Component "Restart"
            }
            catch {
                Write-Host "." -NoNewline
            }
        }

        if (-not $online) {
            throw "Server $ServerName ist nach $TimeoutSeconds Sekunden nicht wieder online gekommen"
        }

        # Zusätzliche Zeit für Services
        Write-ServerLog "Warte auf vollständige Initialisierung von $ServerName" -Level Info -ServerName $ServerName -Component "Restart"
        Start-Sleep -Seconds 30

        return @{
            ServerName = $ServerName
            RestartSuccessful = $true
            TotalTime = ((Get-Date) - $startTime).TotalSeconds
        }
    }
    catch {
        Write-ServerLog "Fehler beim Neustart von ${ServerName}: $_" -Level Error -ServerName $ServerName -Component "Restart"
        return @{
            ServerName = $ServerName
            RestartSuccessful = $false
            Error = $_.Exception.Message
        }
    }
}

function Restart-AllServersOrdered {
    [CmdletBinding()]
    param(
        [PSCredential]$Credential
    )

    if (-not $script:Config) {
        throw "Konfiguration nicht geladen. Verwenden Sie Import-ServerConfig zuerst."
    }

    Write-ServerLog "Starte geordneten Neustart aller Server" -Level Info -Component "Restart"

    $results = @()
    $serverList = $script:Config.ServerList | Sort-Object Priority

    foreach ($server in $serverList) {
        Write-ServerLog "Starte Neustart von $($server.Name) (Priorität: $($server.Priority))" -Level Info -ServerName $server.Name -Component "Restart"

        $result = Restart-ServerSafe -ServerName $server.Name -Credential $Credential -TimeoutSeconds $script:Config.Settings.RestartTimeout
        $results += $result

        if ($result.RestartSuccessful -and $server.RestartDelay -gt 0) {
            Write-ServerLog "Warte $($server.RestartDelay) Sekunden vor dem nächsten Server" -Level Info -Component "Restart"
            Start-Sleep -Seconds $server.RestartDelay
        }
    }

    # Zusammenfassung
    $successful = ($results | Where-Object RestartSuccessful).Count
    $total = $results.Count

    Write-ServerLog "Neustart-Vorgang abgeschlossen: $successful/$total Server erfolgreich" -Level Success -Component "Restart"

    return $results
}

#endregion

#region Notification Functions

function Send-ToastNotification {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Title,

        [Parameter(Mandatory)]
        [string]$Message,

        [ValidateSet('Info', 'Warning', 'Error', 'Success')]
        [string]$Type = 'Info'
    )

    if (-not $script:Config.Settings.EnableToastNotifications) {
        return
    }

    try {
        # Prüfe ob Windows 10/11 Toast Notifications verfügbar sind
        Add-Type -AssemblyName System.Windows.Forms -ErrorAction Stop

        # Fallback: Verwende PowerShell Balloon Tip
        $balloon = New-Object System.Windows.Forms.NotifyIcon
        $balloon.Icon = [System.Drawing.SystemIcons]::Information
        $balloon.BalloonTipTitle = $Title
        $balloon.BalloonTipText = $Message
        $balloon.Visible = $true

        # Icon basierend auf Type
        switch ($Type) {
            'Error' { $balloon.BalloonTipIcon = 'Error' }
            'Warning' { $balloon.BalloonTipIcon = 'Warning' }
            'Success' { $balloon.BalloonTipIcon = 'Info' }
            default { $balloon.BalloonTipIcon = 'Info' }
        }

        $balloon.ShowBalloonTip(5000)

        # Cleanup nach kurzer Zeit
        Start-Sleep -Seconds 1
        $balloon.Dispose()

        Write-ServerLog "Benachrichtigung gesendet: $Title" -Level Info -Component "Notification"
    }
    catch {
        # Stille Behandlung - Benachrichtigungen sind optional
        Write-ServerLog "Benachrichtigung nicht verfügbar (normal auf Server-Systemen)" -Level Debug -Component "Notification"
    }
}

function Get-ServerDiskInfo {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,

        [PSCredential]$Credential,

        [switch]$IncludeAllDriveTypes
    )

    Write-ServerLog "Analysiere Festplatten auf $ServerName" -Level Info -ServerName $ServerName -Component "DiskInfo"

    try {
        $diskInfo = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock {
            param($IncludeAll)

            # Filter für Laufwerkstypen
            if ($IncludeAll) {
                $disks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.Size -gt 0 }
            } else {
                # Nur Festplatten (DriveType 3)
                $disks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 -and $_.Size -gt 0 }
            }

            $results = @()
            foreach ($disk in $disks) {
                $driveType = switch ($disk.DriveType) {
                    2 { "Wechseldatenträger" }
                    3 { "Festplatte" }
                    4 { "Netzlaufwerk" }
                    5 { "CD/DVD" }
                    6 { "RAM-Disk" }
                    default { "Unbekannt ($($disk.DriveType))" }
                }

                $results += [PSCustomObject]@{
                    Drive = $disk.DeviceID
                    Label = $disk.VolumeName
                    DriveType = $driveType
                    FileSystem = $disk.FileSystem
                    TotalSpaceGB = [math]::Round($disk.Size / 1GB, 2)
                    FreeSpaceGB = [math]::Round($disk.FreeSpace / 1GB, 2)
                    UsedSpaceGB = [math]::Round(($disk.Size - $disk.FreeSpace) / 1GB, 2)
                    UsedPercent = if ($disk.Size -gt 0) { [math]::Round((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100, 1) } else { 0 }
                    Status = if ($disk.Size -eq 0) { "Nicht verfügbar" }
                            elseif ((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100 -gt 90) { "Kritisch" }
                            elseif ((($disk.Size - $disk.FreeSpace) / $disk.Size) * 100 -gt 80) { "Warnung" }
                            else { "OK" }
                }
            }

            return $results | Sort-Object Drive
        } -ArgumentList $IncludeAllDriveTypes.IsPresent -ErrorAction Stop

        Write-ServerLog "Festplatten-Analyse abgeschlossen: $($diskInfo.Count) Laufwerke gefunden" -Level Success -ServerName $ServerName -Component "DiskInfo"

        return $diskInfo
    }
    catch {
        Write-ServerLog "Fehler bei Festplatten-Analyse auf ${ServerName}: $_" -Level Error -ServerName $ServerName -Component "DiskInfo"
        throw
    }
}

function Show-DiskSpaceReport {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,

        [PSCredential]$Credential,

        [switch]$IncludeAllDriveTypes,

        [switch]$ShowWarningsOnly
    )

    try {
        $diskInfo = Get-ServerDiskInfo -ServerName $ServerName -Credential $Credential -IncludeAllDriveTypes:$IncludeAllDriveTypes

        Write-Host ""
        Write-Host "FESTPLATTEN-BERICHT: $ServerName" -ForegroundColor White
        Write-Host "─────────────────────────────────" -ForegroundColor Gray

        foreach ($disk in $diskInfo) {
            # Filter für nur Warnungen
            if ($ShowWarningsOnly -and $disk.Status -eq "OK") {
                continue
            }

            $color = switch ($disk.Status) {
                "Kritisch" { 'Red' }
                "Warnung" { 'Yellow' }
                "OK" { 'Green' }
                default { 'Gray' }
            }

            $label = if ($disk.Label) { " ($($disk.Label))" } else { "" }
            $fileSystem = if ($disk.FileSystem) { " [$($disk.FileSystem)]" } else { "" }

            Write-Host "$($disk.Drive.PadRight(3)) $($disk.DriveType.PadRight(15)) $($disk.FreeSpaceGB)GB frei / $($disk.TotalSpaceGB)GB ($($disk.UsedPercent)%)$label$fileSystem" -ForegroundColor $color

            if ($disk.Status -ne "OK") {
                Write-Host "    Status: $($disk.Status)" -ForegroundColor $color
            }
        }

        # Zusammenfassung
        $criticalDisks = ($diskInfo | Where-Object Status -eq "Kritisch").Count
        $warningDisks = ($diskInfo | Where-Object Status -eq "Warnung").Count
        $totalDisks = $diskInfo.Count

        Write-Host ""
        Write-Host "Zusammenfassung: $totalDisks Laufwerke" -ForegroundColor Gray
        if ($criticalDisks -gt 0) {
            Write-Host "  $criticalDisks kritische Laufwerke" -ForegroundColor Red
        }
        if ($warningDisks -gt 0) {
            Write-Host "  $warningDisks Laufwerke mit Warnung" -ForegroundColor Yellow
        }
        if ($criticalDisks -eq 0 -and $warningDisks -eq 0) {
            Write-Host "  Alle Laufwerke OK" -ForegroundColor Green
        }

        return $diskInfo
    }
    catch {
        Write-Host "✗ Fehler beim Abrufen der Festplatten-Informationen: $_" -ForegroundColor Red
        return $null
    }
}

#endregion

# Exportiere die öffentlichen Funktionen
Export-ModuleMember -Function @(
    'Write-ServerLog',
    'Initialize-Logging',
    'Import-ServerConfig',
    'Get-ServerCredentials',
    'Test-ServerConnectivity',
    'Test-AllServers',
    'Search-WindowsUpdatesModern',
    'Install-WindowsUpdatesModern',
    'Restart-ServerSafe',
    'Restart-AllServersOrdered',
    'Send-ToastNotification',
    'Get-ServerDiskInfo',
    'Show-DiskSpaceReport'
)
