#Requires -Version 7.0
<#
.SYNOPSIS
    Erweiterte Funktionen für das Windows Server Management
.DESCRIPTION
    Zusätzliche Tools und Funktionen für die erweiterte Server-Verwaltung
.AUTHOR
    Tonino Gerns
.VERSION
    2.0.0
#>

# Erweiterte Server-Monitoring Funktionen

function Get-ServerPerformanceCounters {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,
        
        [PSCredential]$Credential
    )
    
    Write-Host "📊 Sammle Performance-Daten von $ServerName..." -ForegroundColor Cyan
    
    try {
        $perfData = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock {
            # CPU Auslastung
            $cpu = Get-Counter '\Processor(_Total)\% Processor Time' -SampleInterval 1 -MaxSamples 3
            $avgCpu = ($cpu.CounterSamples | Measure-Object CookedValue -Average).Average
            
            # Memory Auslastung
            $totalMemory = (Get-CimInstance Win32_ComputerSystem).TotalPhysicalMemory
            $availableMemory = (Get-CimInstance Win32_OperatingSystem).FreePhysicalMemory * 1KB
            $usedMemoryPercent = (($totalMemory - $availableMemory) / $totalMemory) * 100
            
            # Disk I/O
            $diskReads = Get-Counter '\PhysicalDisk(_Total)\Disk Reads/sec' -SampleInterval 1 -MaxSamples 1
            $diskWrites = Get-Counter '\PhysicalDisk(_Total)\Disk Writes/sec' -SampleInterval 1 -MaxSamples 1
            
            # Network I/O
            $networkIn = Get-Counter '\Network Interface(*)\Bytes Received/sec' -SampleInterval 1 -MaxSamples 1
            $networkOut = Get-Counter '\Network Interface(*)\Bytes Sent/sec' -SampleInterval 1 -MaxSamples 1
            
            return @{
                CPUUsage = [math]::Round($avgCpu, 2)
                MemoryUsage = [math]::Round($usedMemoryPercent, 2)
                DiskReadsPerSec = [math]::Round($diskReads.CounterSamples[0].CookedValue, 2)
                DiskWritesPerSec = [math]::Round($diskWrites.CounterSamples[0].CookedValue, 2)
                NetworkInBytesPerSec = ($networkIn.CounterSamples | Measure-Object CookedValue -Sum).Sum
                NetworkOutBytesPerSec = ($networkOut.CounterSamples | Measure-Object CookedValue -Sum).Sum
                Timestamp = Get-Date
            }
        }
        
        Write-Host "`n=== PERFORMANCE DATEN für $ServerName ===" -ForegroundColor Green
        Write-Host "CPU Auslastung: $($perfData.CPUUsage)%" -ForegroundColor $(if($perfData.CPUUsage -gt 80) {'Red'} elseif($perfData.CPUUsage -gt 60) {'Yellow'} else {'Green'})
        Write-Host "Memory Auslastung: $($perfData.MemoryUsage)%" -ForegroundColor $(if($perfData.MemoryUsage -gt 90) {'Red'} elseif($perfData.MemoryUsage -gt 80) {'Yellow'} else {'Green'})
        Write-Host "Disk Reads/sec: $($perfData.DiskReadsPerSec)" -ForegroundColor White
        Write-Host "Disk Writes/sec: $($perfData.DiskWritesPerSec)" -ForegroundColor White
        Write-Host "Network In: $([math]::Round($perfData.NetworkInBytesPerSec / 1MB, 2)) MB/s" -ForegroundColor White
        Write-Host "Network Out: $([math]::Round($perfData.NetworkOutBytesPerSec / 1MB, 2)) MB/s" -ForegroundColor White
        
        return $perfData
    }
    catch {
        Write-Host "❌ Fehler beim Sammeln der Performance-Daten: $_" -ForegroundColor Red
        throw
    }
}

function Get-ServerEventLogs {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,
        
        [PSCredential]$Credential,
        
        [int]$Hours = 24,
        
        [ValidateSet('System', 'Application', 'Security')]
        [string[]]$LogNames = @('System', 'Application')
    )
    
    Write-Host "📋 Analysiere Event Logs von $ServerName (letzte $Hours Stunden)..." -ForegroundColor Cyan
    
    try {
        $events = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock {
            param($LogNames, $Hours)
            
            $startTime = (Get-Date).AddHours(-$Hours)
            $allEvents = @()
            
            foreach ($logName in $LogNames) {
                $events = Get-WinEvent -FilterHashtable @{
                    LogName = $logName
                    StartTime = $startTime
                    Level = @(1,2,3)  # Critical, Error, Warning
                } -ErrorAction SilentlyContinue
                
                $allEvents += $events | Select-Object TimeCreated, Id, LevelDisplayName, LogName, Message
            }
            
            return $allEvents | Sort-Object TimeCreated -Descending
        } -ArgumentList $LogNames, $Hours
        
        if ($events.Count -gt 0) {
            Write-Host "`n=== EVENT LOG ANALYSE für $ServerName ===" -ForegroundColor Yellow
            
            # Gruppierung nach Level
            $critical = $events | Where-Object LevelDisplayName -eq 'Critical'
            $errors = $events | Where-Object LevelDisplayName -eq 'Error'  
            $warnings = $events | Where-Object LevelDisplayName -eq 'Warning'
            
            Write-Host "🔴 Critical: $($critical.Count)" -ForegroundColor Red
            Write-Host "❌ Errors: $($errors.Count)" -ForegroundColor Red
            Write-Host "⚠️  Warnings: $($warnings.Count)" -ForegroundColor Yellow
            
            # Top 10 neueste kritische/Fehler Events
            $topEvents = ($critical + $errors) | Select-Object -First 10
            if ($topEvents.Count -gt 0) {
                Write-Host "`n=== TOP KRITISCHE EVENTS ===" -ForegroundColor Red
                $topEvents | Format-Table -AutoSize TimeCreated, Id, LevelDisplayName, LogName, @{Name='Message';Expression={$_.Message.Substring(0,[Math]::Min(80,$_.Message.Length))}}
            }
        } else {
            Write-Host "✅ Keine kritischen Events in den letzten $Hours Stunden gefunden" -ForegroundColor Green
        }
        
        return $events
    }
    catch {
        Write-Host "❌ Fehler beim Analysieren der Event Logs: $_" -ForegroundColor Red
        throw
    }
}

function Test-ServerServices {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,
        
        [PSCredential]$Credential,
        
        [string[]]$CriticalServices = @(
            'Spooler', 'DHCP Client', 'DNS Client', 'Windows Time',
            'Remote Desktop Services', 'Windows Update', 'Task Scheduler'
        )
    )
    
    Write-Host "🔧 Prüfe Services auf $ServerName..." -ForegroundColor Cyan
    
    try {
        $serviceStatus = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock {
            param($CriticalServices)
            
            $results = @()
            
            foreach ($serviceName in $CriticalServices) {
                $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
                if ($service) {
                    $results += @{
                        Name = $service.Name
                        DisplayName = $service.DisplayName
                        Status = $service.Status
                        StartType = $service.StartType
                        IsCritical = $true
                    }
                }
            }
            
            # Alle gestoppten automatischen Services
            $stoppedAuto = Get-Service | Where-Object { $_.Status -eq 'Stopped' -and $_.StartType -eq 'Automatic' }
            foreach ($service in $stoppedAuto) {
                if ($service.Name -notin $CriticalServices) {
                    $results += @{
                        Name = $service.Name
                        DisplayName = $service.DisplayName
                        Status = $service.Status
                        StartType = $service.StartType
                        IsCritical = $false
                    }
                }
            }
            
            return $results
        } -ArgumentList $CriticalServices
        
        Write-Host "`n=== SERVICE STATUS für $ServerName ===" -ForegroundColor Green
        
        # Kritische Services prüfen
        $criticalStopped = $serviceStatus | Where-Object { $_.IsCritical -and $_.Status -eq 'Stopped' }
        if ($criticalStopped.Count -gt 0) {
            Write-Host "`n🔴 KRITISCHE GESTOPPTE SERVICES:" -ForegroundColor Red
            $criticalStopped | Format-Table -AutoSize Name, DisplayName, Status
            
            # Angebot zum Starten
            $restart = Read-Host "Möchten Sie die kritischen Services starten? (j/n)"
            if ($restart -eq 'j') {
                foreach ($service in $criticalStopped) {
                    try {
                        Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock {
                            param($ServiceName)
                            Start-Service -Name $ServiceName
                        } -ArgumentList $service.Name
                        Write-Host "✅ Service '$($service.Name)' gestartet" -ForegroundColor Green
                    }
                    catch {
                        Write-Host "❌ Fehler beim Starten von '$($service.Name)': $_" -ForegroundColor Red
                    }
                }
            }
        } else {
            Write-Host "✅ Alle kritischen Services laufen" -ForegroundColor Green
        }
        
        # Andere gestoppte automatische Services
        $otherStopped = $serviceStatus | Where-Object { -not $_.IsCritical }
        if ($otherStopped.Count -gt 0) {
            Write-Host "`n⚠️  ANDERE GESTOPPTE AUTOMATISCHE SERVICES:" -ForegroundColor Yellow
            $otherStopped | Format-Table -AutoSize Name, DisplayName, Status
        }
        
        return $serviceStatus
    }
    catch {
        Write-Host "❌ Fehler beim Prüfen der Services: $_" -ForegroundColor Red
        throw
    }
}

function Export-ServerReport {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$ServerName,
        
        [PSCredential]$Credential,
        
        [string]$OutputPath = "C:\Reports"
    )
    
    Write-Host "📄 Erstelle detaillierten Bericht für $ServerName..." -ForegroundColor Cyan
    
    try {
        # Ausgabeverzeichnis erstellen
        if (-not (Test-Path $OutputPath)) {
            New-Item -Path $OutputPath -ItemType Directory -Force | Out-Null
        }
        
        $timestamp = Get-Date -Format 'yyyyMMdd_HHmmss'
        $reportFile = Join-Path $OutputPath "ServerReport_${ServerName}_${timestamp}.json"
        
        # Daten sammeln
        Write-Host "Sammle Systeminformationen..." -ForegroundColor White
        $systemInfo = Invoke-Command -ComputerName $ServerName -Credential $Credential -ScriptBlock {
            $os = Get-CimInstance Win32_OperatingSystem
            $computer = Get-CimInstance Win32_ComputerSystem
            $bios = Get-CimInstance Win32_BIOS
            $disks = Get-CimInstance Win32_LogicalDisk | Where-Object DriveType -eq 3
            $network = Get-CimInstance Win32_NetworkAdapterConfiguration | Where-Object IPEnabled -eq $true
            
            return @{
                ComputerName = $env:COMPUTERNAME
                OS = @{
                    Name = $os.Caption
                    Version = $os.Version
                    BuildNumber = $os.BuildNumber
                    InstallDate = $os.InstallDate
                    LastBootUpTime = $os.LastBootUpTime
                }
                Hardware = @{
                    Manufacturer = $computer.Manufacturer
                    Model = $computer.Model
                    TotalPhysicalMemory = $computer.TotalPhysicalMemory
                    NumberOfProcessors = $computer.NumberOfProcessors
                    SerialNumber = $bios.SerialNumber
                }
                Disks = $disks | ForEach-Object {
                    @{
                        Drive = $_.DeviceID
                        Size = $_.Size
                        FreeSpace = $_.FreeSpace
                        FileSystem = $_.FileSystem
                    }
                }
                Network = $network | ForEach-Object {
                    @{
                        Description = $_.Description
                        IPAddress = $_.IPAddress
                        SubnetMask = $_.IPSubnet
                        DefaultGateway = $_.DefaultIPGateway
                        DNSServers = $_.DNSServerSearchOrder
                    }
                }
            }
        }
        
        Write-Host "Sammle Performance-Daten..." -ForegroundColor White
        $perfData = Get-ServerPerformanceCounters -ServerName $ServerName -Credential $Credential
        
        Write-Host "Sammle Service-Informationen..." -ForegroundColor White
        $serviceData = Test-ServerServices -ServerName $ServerName -Credential $Credential
        
        Write-Host "Sammle Event Log Daten..." -ForegroundColor White
        $eventData = Get-ServerEventLogs -ServerName $ServerName -Credential $Credential -Hours 24
        
        # Bericht zusammenstellen
        $report = @{
            ServerName = $ServerName
            ReportDate = Get-Date
            SystemInfo = $systemInfo
            Performance = $perfData
            Services = $serviceData
            Events = @{
                Critical = ($eventData | Where-Object LevelDisplayName -eq 'Critical').Count
                Errors = ($eventData | Where-Object LevelDisplayName -eq 'Error').Count
                Warnings = ($eventData | Where-Object LevelDisplayName -eq 'Warning').Count
                RecentCritical = $eventData | Where-Object LevelDisplayName -eq 'Critical' | Select-Object -First 5
            }
        }
        
        # Als JSON exportieren
        $report | ConvertTo-Json -Depth 10 | Out-File -FilePath $reportFile -Encoding UTF8
        
        Write-Host "✅ Bericht erstellt: $reportFile" -ForegroundColor Green
        
        # HTML-Version erstellen
        $htmlFile = $reportFile -replace '\.json$', '.html'
        $htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>Server Report - $ServerName</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .critical { background-color: #ffebee; }
        .warning { background-color: #fff3e0; }
        .success { background-color: #e8f5e8; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Server Report: $ServerName</h1>
        <p>Erstellt am: $(Get-Date -Format 'dd.MM.yyyy HH:mm:ss')</p>
    </div>
    
    <div class="section">
        <h2>System Information</h2>
        <table>
            <tr><th>Eigenschaft</th><th>Wert</th></tr>
            <tr><td>Betriebssystem</td><td>$($systemInfo.OS.Name)</td></tr>
            <tr><td>Version</td><td>$($systemInfo.OS.Version)</td></tr>
            <tr><td>Hersteller</td><td>$($systemInfo.Hardware.Manufacturer)</td></tr>
            <tr><td>Modell</td><td>$($systemInfo.Hardware.Model)</td></tr>
            <tr><td>RAM</td><td>$([math]::Round($systemInfo.Hardware.TotalPhysicalMemory / 1GB, 2)) GB</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>Performance</h2>
        <table>
            <tr><th>Metrik</th><th>Wert</th></tr>
            <tr><td>CPU Auslastung</td><td>$($perfData.CPUUsage)%</td></tr>
            <tr><td>Memory Auslastung</td><td>$($perfData.MemoryUsage)%</td></tr>
            <tr><td>Disk Reads/sec</td><td>$($perfData.DiskReadsPerSec)</td></tr>
            <tr><td>Disk Writes/sec</td><td>$($perfData.DiskWritesPerSec)</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>Event Summary</h2>
        <table>
            <tr><th>Level</th><th>Anzahl (24h)</th></tr>
            <tr><td>Critical</td><td style="color: red;">$($report.Events.Critical)</td></tr>
            <tr><td>Errors</td><td style="color: red;">$($report.Events.Errors)</td></tr>
            <tr><td>Warnings</td><td style="color: orange;">$($report.Events.Warnings)</td></tr>
        </table>
    </div>
</body>
</html>
"@
        
        $htmlContent | Out-File -FilePath $htmlFile -Encoding UTF8
        Write-Host "✅ HTML-Bericht erstellt: $htmlFile" -ForegroundColor Green
        
        return @{
            JsonReport = $reportFile
            HtmlReport = $htmlFile
            Data = $report
        }
    }
    catch {
        Write-Host "❌ Fehler beim Erstellen des Berichts: $_" -ForegroundColor Red
        throw
    }
}

# Exportiere die Funktionen
Export-ModuleMember -Function @(
    'Get-ServerPerformanceCounters',
    'Get-ServerEventLogs',
    'Test-ServerServices',
    'Export-ServerReport'
)
