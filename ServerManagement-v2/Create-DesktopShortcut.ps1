# Desktop-Verknüpfung erstellen
$shell = New-Object -ComObject WScript.Shell
$desktopPath = [Environment]::GetFolderPath("Desktop")
$shortcutPath = Join-Path $desktopPath "Server Management v2.0.lnk"

$shortcut = $shell.CreateShortcut($shortcutPath)
$shortcut.TargetPath = Join-Path $PSScriptRoot "START-ServerManagement.bat"
$shortcut.WorkingDirectory = $PSScriptRoot
$shortcut.Description = "Windows Server Management v2.0 - Moderne Server-Verwaltung"
$shortcut.IconLocation = "shell32.dll,21"
$shortcut.Save()

Write-Host "✅ Desktop-Verknüpfung erstellt: $shortcutPath" -ForegroundColor Green
Write-Host "Sie können jetzt das Server Management über die Desktop-Verknüpfung starten!" -ForegroundColor Cyan
