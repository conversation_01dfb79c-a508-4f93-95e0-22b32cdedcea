# 🚀 Windows Server Management v2.0 - Schnellstart

## 📁 Ordnerstruktur

```
ServerManagement-v2/
├── START-ServerManagement.bat    ← **DIESE DATEI AUSFÜHREN!**
├── ServerManagement.ps1          ← Hauptskript (wird automatisch gestartet)
├── Setup-ServerManagement.ps1    ← Einmaliges Setup (optional)
├── ANLEITUNG.md                  ← Diese Datei
├── README.md                     ← Vollständige Dokumentation
├── Config/
│   └── ServerConfig.psd1         ← Server-Konfiguration (anpassen!)
├── Modules/
│   ├── ServerManagement.psm1     ← Kern-Funktionen
│   └── ExtendedFeatures.ps1      ← Erweiterte Features
├── Logs/                         ← Automatische Log-Dateien
└── Reports/                      ← Generierte Berichte
```

## 🎯 SO STARTEN SIE DAS SYSTEM:

### 1. **Doppelklick auf `START-ServerManagement.bat`**
   - Das ist die EINZIGE Datei, die Sie ausführen müssen!
   - Startet automatisch PowerShell 7 mit dem Server Management
   - <PERSON>ine Administratorrechte erforderlich

### 2. **Erste Konfiguration**
   - Bearbeiten Sie `Config\ServerConfig.psd1` für Ihre Server
   - Standard-Server sind bereits eingetragen (DC01, DC02, DB01, APP01, MAIL, FILE, TS01)
   - Neustart-Reihenfolge ist bereits korrekt konfiguriert

### 3. **Credentials eingeben**
   - Das Skript fragt nach Ihren Administrator-Credentials
   - Diese werden für alle Remote-Operationen verwendet
   - Standard-Benutzer: "edv" (kann in Config geändert werden)

## 🎮 Interaktives Menü

Nach dem Start haben Sie folgende Optionen:

```
=== HAUPTMENÜ ===
1. 🌐 Alle Server anpingen
2. 🔍 Windows Updates suchen  
3. 📦 Windows Updates installieren (OHNE Autostart!)
4. 🔄 Server neu starten (intelligente Reihenfolge)
5. 📊 Server-Status anzeigen
6. 🛠️  Erweiterte Funktionen
7. ⚙️  Einstellungen
8. 📋 Berichte anzeigen
9. ❌ Beenden
```

## ⚙️ Wichtige Einstellungen

### Server-Neustart-Reihenfolge:
1. **DC01** → 3 Min Wartezeit
2. **DC02** → 2 Min Wartezeit  
3. **DB01** → 3 Min Wartezeit
4. **APP01** → 2 Min Wartezeit
5. **MAIL** → 2 Min Wartezeit
6. **FILE** → 1.5 Min Wartezeit
7. **TS01** → Fertig

### Windows Updates:
- ✅ **OHNE automatischen Neustart** (wie gewünscht!)
- ✅ Zeigt an, welche Server einen Neustart benötigen
- ✅ Neustart erfolgt nur auf explizite Anfrage

### Benachrichtigungen:
- ✅ Windows 11 Toast-Benachrichtigungen
- ✅ Automatische Meldung wenn Server wieder online sind
- ✅ Farbkodierte Status-Anzeigen

## 🔧 Anpassungen

### Server-Liste ändern:
1. Öffnen Sie `Config\ServerConfig.psd1`
2. Bearbeiten Sie die `ServerList` Sektion
3. Speichern und Skript neu starten

### Beispiel Server-Eintrag:
```powershell
@{
    Name = "DC01"
    Role = "DomainController"
    Priority = 1
    RestartDelay = 180  # 3 Minuten warten nach Neustart
}
```

## 🆘 Problemlösung

### PowerShell 7 nicht gefunden:
```cmd
winget install Microsoft.PowerShell
```

### WinRM nicht konfiguriert:
```powershell
# Auf jedem Zielserver ausführen:
Enable-PSRemoting -Force
```

### Firewall-Probleme:
```powershell
# Auf jedem Zielserver:
Enable-NetFirewallRule -DisplayGroup "Windows Remote Management"
```

## 📋 Logs und Berichte

- **Logs**: Automatisch in `Logs\` Ordner
- **Berichte**: Werden in `Reports\` gespeichert
- **Format**: JSON und HTML verfügbar

## 🎯 Zusammenfassung

**Sie müssen nur:**
1. `START-ServerManagement.bat` doppelklicken
2. Ihre Admin-Credentials eingeben
3. Das interaktive Menü verwenden

**Das System macht automatisch:**
- ✅ PowerShell 7 starten
- ✅ Alle Module laden
- ✅ Konfiguration einlesen
- ✅ Interaktives Menü anzeigen
- ✅ Logs schreiben
- ✅ Benachrichtigungen senden

---

**🎉 Viel Erfolg mit dem neuen Server Management System!**
