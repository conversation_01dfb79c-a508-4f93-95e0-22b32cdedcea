@echo off
title Windows Server Management GUI v2.0 - Simple Version
echo.
echo ========================================
echo  Windows Server Management GUI v2.0
echo  Simple Version (Funktioniert garantiert!)
echo ========================================
echo.

REM Prüfe ob die einfache GUI-Version existiert
if exist "%~dp0SimpleGUI\publish\SimpleServerGUI.exe" (
    echo Starte einfache GUI-Version...
    echo.
    echo HINWEISE:
    echo - Diese Version zeigt die moderne GUI-Oberflaeche
    echo - Ping-Tests funktionieren vollstaendig
    echo - Fuer vollstaendige PowerShell-Integration verwenden Sie:
    echo   START-ServerManagement.bat
    echo.
    cd /d "%~dp0SimpleGUI\publish"
    SimpleServerGUI.exe
    goto :end
)

echo FEHLER: Einfache GUI-Version nicht gefunden!
echo.
echo Bitte kompilieren Sie die Anwendung zuerst:
echo cd SimpleGUI
echo dotnet publish --configuration Release --output publish --self-contained true --runtime win-x64
echo.
pause

:end
if %errorlevel% neq 0 (
    echo.
    echo Ein Fehler ist aufgetreten. Druecken Sie eine Taste zum Beenden...
    pause >nul
)
