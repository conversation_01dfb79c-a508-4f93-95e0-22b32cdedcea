#Requires -Version 7.0
<#
.SYNOPSIS
    Modernes Windows Server 2022 Verwaltungsskript
.DESCRIPTION
    Modernisiertes PowerShell-Skript für die zuverlässige Verwaltung von Windows Server 2022
    von einem Windows 11 Client aus. Bietet erweiterte Funktionen für Updates, Neustarts,
    Monitoring und Benachrichtigungen.
.AUTHOR
    Tonino Gerns
.VERSION
    2.0.0
.NOTES
    Erfordert PowerShell 7.0+ und Administratorrechte
#>

[CmdletBinding()]
param(
    [string]$ConfigPath = ".\Config\ServerConfig.psd1",
    [switch]$NoInteractive
)

# Prüfe PowerShell Version
if ($PSVersionTable.PSVersion.Major -lt 7) {
    Write-Error "Dieses Skript erfordert PowerShell 7.0 oder höher. Aktuelle Version: $($PSVersionTable.PSVersion)"
    exit 1
}

# Hinweis: Skript läuft als normaler Benutzer, Credentials werden für Remote-Zugriff verwendet
Write-Host "ℹ️  Dieses Skript läuft als normaler Benutzer. Administrative Aufgaben werden über Remote-Credentials ausgeführt." -ForegroundColor Cyan

# Schlichter Header
function Show-Header {
    Clear-Host
    Write-Host ""
    Write-Host "Windows Server Management v2.0" -ForegroundColor White
    Write-Host "$(Get-Date -Format 'dd.MM.yyyy HH:mm')" -ForegroundColor Gray
    Write-Host ""
}

# Module importieren
try {
    $modulePath = Join-Path $PSScriptRoot "Modules\ServerManagement.psm1"
    if (-not (Test-Path $modulePath)) {
        throw "ServerManagement.psm1 nicht gefunden in: $modulePath"
    }

    Import-Module $modulePath -Force

    # Erweiterte Features laden (optional)
    $extendedPath = Join-Path $PSScriptRoot "Modules\ExtendedFeatures.psm1"
    if (Test-Path $extendedPath) {
        try {
            Import-Module $extendedPath -Force
        }
        catch {
            # Stille Behandlung
        }
    }
}
catch {
    Write-Error "Fehler beim Laden der Module: $_"
    exit 1
}

# Konfiguration laden
try {
    if (-not (Test-Path $ConfigPath)) {
        throw "Konfigurationsdatei nicht gefunden: $ConfigPath"
    }

    $config = Import-ServerConfig -ConfigPath $ConfigPath
}
catch {
    Write-Error "Fehler beim Laden der Konfiguration: $_"
    exit 1
}

# Hauptmenü
function Show-MainMenu {
    Write-Host ""
    Write-Host "HAUPTMENÜ" -ForegroundColor White
    Write-Host "─────────" -ForegroundColor Gray
    Write-Host "1. Server anpingen" -ForegroundColor Gray
    Write-Host "2. Updates suchen" -ForegroundColor Gray
    Write-Host "3. Updates installieren" -ForegroundColor Gray
    Write-Host "4. Server neu starten" -ForegroundColor Gray
    Write-Host "5. Server-Status" -ForegroundColor Gray
    Write-Host "6. Erweiterte Funktionen" -ForegroundColor Gray
    Write-Host "7. Einstellungen" -ForegroundColor Gray
    Write-Host "8. Berichte" -ForegroundColor Gray
    Write-Host "9. Beenden" -ForegroundColor Gray
    Write-Host ""
}

# Server-Auswahlmenü
function Show-ServerSelectionMenu {
    Write-Host ""
    Write-Host "SERVER AUSWAHL" -ForegroundColor White
    Write-Host "──────────────" -ForegroundColor Gray
    Write-Host "1. Alle Server" -ForegroundColor Gray
    Write-Host "2. Einzelnen Server" -ForegroundColor Gray
    Write-Host "3. Server-Liste anzeigen" -ForegroundColor Gray
    Write-Host "4. Zurück" -ForegroundColor Gray
    Write-Host ""

    $choice = Read-Host "Auswahl (1-4)"

    switch ($choice) {
        "1" { return "all" }
        "2" {
            Show-ServerList
            $serverName = Read-Host "Servername"
            return $serverName
        }
        "3" {
            Show-ServerList
            return Show-ServerSelectionMenu
        }
        "4" { return "back" }
        default {
            Write-Host "Ungültige Auswahl" -ForegroundColor Red
            return Show-ServerSelectionMenu
        }
    }
}

function Show-ServerList {
    Write-Host ""
    Write-Host "VERFÜGBARE SERVER" -ForegroundColor White
    Write-Host "─────────────────" -ForegroundColor Gray
    $config.ServerList | ForEach-Object -Begin { $i = 1 } -Process {
        Write-Host "$i. $($_.Name.PadRight(8)) $($_.Role)" -ForegroundColor Gray
        $i++
    }
    Write-Host ""
}

# Ping-Funktion
function Invoke-PingServers {
    param([string]$Selection)

    Write-Host "Teste Server-Verbindungen..." -ForegroundColor White

    if ($Selection -eq "all") {
        $results = Test-AllServers
    } else {
        $result = Test-ServerConnectivity -ServerName $Selection
        if ($result.IsOnline) {
            Write-Host ""
            Write-Host "✓ $($result.ServerName.PadRight(8)) Online  ($($result.ResponseTime)ms)" -ForegroundColor Green
        } else {
            Write-Host ""
            Write-Host "✗ $($result.ServerName.PadRight(8)) Offline" -ForegroundColor Red
        }
        $results = @($result)
    }

    # Toast-Benachrichtigung
    $onlineCount = ($results | Where-Object IsOnline).Count
    $totalCount = $results.Count
    Send-ToastNotification -Title "Server Ping" -Message "$onlineCount von $totalCount online" -Type Success

    Write-Host ""
    Read-Host "Weiter mit Enter"
}

# Windows Update Suche
function Invoke-SearchUpdates {
    param([string]$Selection)

    Write-Host "Suche Windows Updates..." -ForegroundColor White

    $credentials = Get-ServerCredentials

    if ($Selection -eq "all") {
        $serverList = $config.ServerList

        if ($config.Features.ParallelProcessing) {
            $results = $serverList | ForEach-Object -Parallel {
                $server = $_
                $cred = $using:credentials

                $modulePath = Join-Path $using:PSScriptRoot "Modules\ServerManagement.psm1"
                Import-Module $modulePath -Force

                try {
                    $updates = Search-WindowsUpdatesModern -ServerName $server.Name -Credential $cred
                    return @{
                        ServerName = $server.Name
                        Updates = $updates
                        Success = $true
                    }
                }
                catch {
                    return @{
                        ServerName = $server.Name
                        Error = $_.Exception.Message
                        Success = $false
                    }
                }
            } -ThrottleLimit $config.Features.MaxParallelJobs
        } else {
            $results = @()
            foreach ($server in $serverList) {
                try {
                    $updates = Search-WindowsUpdatesModern -ServerName $server.Name -Credential $credentials
                    $results += @{
                        ServerName = $server.Name
                        Updates = $updates
                        Success = $true
                    }
                }
                catch {
                    $results += @{
                        ServerName = $server.Name
                        Error = $_.Exception.Message
                        Success = $false
                    }
                }
            }
        }

        # Schlichte Ergebnisanzeige
        Write-Host ""
        foreach ($result in $results) {
            if ($result.Success) {
                if ($result.Updates.Count -gt 0) {
                    Write-Host "✓ $($result.ServerName.PadRight(8)) $($result.Updates.Count) Updates verfügbar" -ForegroundColor Yellow
                } else {
                    Write-Host "✓ $($result.ServerName.PadRight(8)) Keine Updates" -ForegroundColor Green
                }
            } else {
                Write-Host "✗ $($result.ServerName.PadRight(8)) Fehler" -ForegroundColor Red
            }
        }
    } else {
        try {
            $updates = Search-WindowsUpdatesModern -ServerName $Selection -Credential $credentials
            Write-Host ""
            if ($updates.Count -gt 0) {
                Write-Host "✓ $($Selection.PadRight(8)) $($updates.Count) Updates verfügbar" -ForegroundColor Yellow
                Write-Host ""
                $updates | Format-Table -AutoSize @{Name='Titel';Expression={$_.Title.Substring(0,[Math]::Min(60,$_.Title.Length))}}, @{Name='Größe (MB)';Expression={$_.Size}}, @{Name='KB';Expression={$_.KBArticleIDs}}
            } else {
                Write-Host "✓ $($Selection.PadRight(8)) Keine Updates" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "✗ $($Selection.PadRight(8)) Fehler: $_" -ForegroundColor Red
        }
    }

    Write-Host ""
    Read-Host "Weiter mit Enter"
}

# Windows Update Installation
function Invoke-InstallUpdates {
    param([string]$Selection)

    Write-Host "`n📦 Installiere Windows Updates..." -ForegroundColor Cyan
    Write-Host "⚠️  WICHTIG: Updates werden OHNE automatischen Neustart installiert!" -ForegroundColor Yellow

    $confirm = Read-Host "Möchten Sie fortfahren? (j/n)"
    if ($confirm -ne "j") {
        Write-Host "Vorgang abgebrochen." -ForegroundColor Yellow
        return
    }

    $credentials = Get-ServerCredentials

    if ($Selection -eq "all") {
        $serverList = $config.ServerList

        foreach ($server in $serverList) {
            Write-Host "`n🔄 Installiere Updates auf $($server.Name)..." -ForegroundColor Cyan
            try {
                $result = Install-WindowsUpdatesModern -ServerName $server.Name -Credential $credentials -NoReboot

                if ($result.Status -eq "Success") {
                    Write-Host "✅ $($result.UpdatesInstalled) Updates installiert auf $($server.Name)" -ForegroundColor Green
                    if ($result.RebootRequired) {
                        Write-Host "⚠️  Neustart erforderlich für $($server.Name)" -ForegroundColor Yellow
                    }
                } elseif ($result.Status -eq "NoUpdates") {
                    Write-Host "ℹ️  Keine Updates verfügbar für $($server.Name)" -ForegroundColor Blue
                }
            }
            catch {
                Write-Host "❌ Fehler bei $($server.Name): $_" -ForegroundColor Red
            }
        }

        Send-ToastNotification -Title "Update Installation" -Message "Updates auf allen Servern abgeschlossen" -Type Success
    } else {
        Write-Host "`n🔄 Installiere Updates auf $Selection..." -ForegroundColor Cyan
        try {
            $result = Install-WindowsUpdatesModern -ServerName $Selection -Credential $credentials -NoReboot

            if ($result.Status -eq "Success") {
                Write-Host "✅ $($result.UpdatesInstalled) Updates installiert" -ForegroundColor Green
                if ($result.RebootRequired) {
                    Write-Host "⚠️  Neustart erforderlich" -ForegroundColor Yellow
                }
                Send-ToastNotification -Title "Update Installation" -Message "$($result.UpdatesInstalled) Updates auf $Selection installiert" -Type Success
            } elseif ($result.Status -eq "NoUpdates") {
                Write-Host "ℹ️  Keine Updates verfügbar" -ForegroundColor Blue
            }
        }
        catch {
            Write-Host "❌ Fehler: $_" -ForegroundColor Red
        }
    }

    Write-Host "`nDrücken Sie eine beliebige Taste zum Fortfahren..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Server Neustart
function Invoke-RestartServers {
    param([string]$Selection)

    Write-Host "`n🔄 Server Neustart..." -ForegroundColor Cyan

    if ($Selection -eq "all") {
        Write-Host "⚠️  ACHTUNG: Alle Server werden in der korrekten Reihenfolge neu gestartet!" -ForegroundColor Red
        Write-Host "Reihenfolge: DC01 → DC02 → DB01 → APP01 → MAIL → FILE → TS01" -ForegroundColor Yellow

        $confirm = Read-Host "Sind Sie sicher? (JA/nein)"
        if ($confirm -ne "JA") {
            Write-Host "Vorgang abgebrochen." -ForegroundColor Yellow
            return
        }

        $credentials = Get-ServerCredentials

        Write-Host "`n🚀 Starte geordneten Neustart aller Server..." -ForegroundColor Cyan
        $results = Restart-AllServersOrdered -Credential $credentials

        # Ergebnisse anzeigen
        Write-Host "`n📊 NEUSTART-ERGEBNISSE:" -ForegroundColor Cyan
        foreach ($result in $results) {
            if ($result.RestartSuccessful) {
                Write-Host "✅ $($result.ServerName) - Erfolgreich ($('{0:F1}' -f $result.TotalTime)s)" -ForegroundColor Green
            } else {
                Write-Host "❌ $($result.ServerName) - Fehler: $($result.Error)" -ForegroundColor Red
            }
        }

        $successful = ($results | Where-Object RestartSuccessful).Count
        $total = $results.Count
        Send-ToastNotification -Title "Server Neustart" -Message "$successful von $total Servern erfolgreich neu gestartet" -Type Success

    } else {
        Write-Host "⚠️  Server $Selection wird neu gestartet!" -ForegroundColor Yellow
        $confirm = Read-Host "Fortfahren? (j/n)"
        if ($confirm -ne "j") {
            Write-Host "Vorgang abgebrochen." -ForegroundColor Yellow
            return
        }

        $credentials = Get-ServerCredentials

        Write-Host "`n🔄 Starte Neustart von $Selection..." -ForegroundColor Cyan
        $result = Restart-ServerSafe -ServerName $Selection -Credential $credentials

        if ($result.RestartSuccessful) {
            Write-Host "✅ $Selection erfolgreich neu gestartet ($('{0:F1}' -f $result.TotalTime)s)" -ForegroundColor Green
            Send-ToastNotification -Title "Server Neustart" -Message "$Selection erfolgreich neu gestartet" -Type Success
        } else {
            Write-Host "❌ Fehler beim Neustart von $Selection`: $($result.Error)" -ForegroundColor Red
        }
    }

    Write-Host "`nDrücken Sie eine beliebige Taste zum Fortfahren..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Server Status anzeigen
function Invoke-ShowServerStatus {
    param([string]$Selection)

    Write-Host "`n📊 Server-Status wird abgerufen..." -ForegroundColor Cyan

    $credentials = Get-ServerCredentials

    if ($Selection -eq "all") {
        $serverList = $config.ServerList

        Write-Host "`n=== SERVER STATUS ÜBERSICHT ===" -ForegroundColor Cyan

        foreach ($server in $serverList) {
            Write-Host ""
            Write-Host "$($server.Name.PadRight(8)) $($server.Role)" -ForegroundColor White

            # Ping Test
            $pingResult = Test-ServerConnectivity -ServerName $server.Name
            if ($pingResult.IsOnline) {
                Write-Host "  ✓ Online ($($pingResult.ResponseTime)ms)" -ForegroundColor Green

                # Zusätzliche Informationen abrufen (vereinfacht)
                try {
                    $systemInfo = Invoke-Command -ComputerName $server.Name -Credential $credentials -ScriptBlock {
                        # Einfachere WMI-Abfragen für bessere Kompatibilität
                        $os = Get-WmiObject -Class Win32_OperatingSystem -ErrorAction Stop
                        $computer = Get-WmiObject -Class Win32_ComputerSystem -ErrorAction Stop
                        $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object DeviceID -eq "C:" | Select-Object -First 1

                        return @{
                            LastBootTime = $os.ConvertToDateTime($os.LastBootUpTime)
                            TotalMemory = [math]::Round($computer.TotalPhysicalMemory / 1GB, 1)
                            FreeMemory = [math]::Round($os.FreePhysicalMemory / 1KB / 1MB, 0)
                            DiskFreeSpace = [math]::Round($disk.FreeSpace / 1GB, 1)
                            DiskTotalSpace = [math]::Round($disk.Size / 1GB, 1)
                        }
                    } -ErrorAction Stop

                    $uptime = (Get-Date) - $systemInfo.LastBootTime
                    Write-Host "  Uptime: $($uptime.Days)d $($uptime.Hours)h" -ForegroundColor Gray
                    Write-Host "  RAM: $($systemInfo.FreeMemory)MB frei / $($systemInfo.TotalMemory)GB gesamt" -ForegroundColor Gray
                    Write-Host "  Disk C: $($systemInfo.DiskFreeSpace)GB frei / $($systemInfo.DiskTotalSpace)GB gesamt" -ForegroundColor Gray

                    # Warnung bei wenig Speicherplatz
                    $diskUsagePercent = (($systemInfo.DiskTotalSpace - $systemInfo.DiskFreeSpace) / $systemInfo.DiskTotalSpace) * 100
                    if ($diskUsagePercent -gt (100 - $config.Settings.DiskSpaceThreshold)) {
                        Write-Host "  ⚠ Wenig Speicherplatz!" -ForegroundColor Yellow
                    }
                }
                catch {
                    Write-Host "  ✗ Systeminformationen nicht verfügbar" -ForegroundColor Red
                    Write-Host "    (WinRM/PowerShell Remoting Problem)" -ForegroundColor Gray
                }
            } else {
                Write-Host "  ✗ Offline" -ForegroundColor Red
            }
        }
    } else {
        Write-Host ""
        Write-Host "DETAILLIERTER STATUS: $Selection" -ForegroundColor White
        Write-Host "─────────────────────────────────" -ForegroundColor Gray

        $pingResult = Test-ServerConnectivity -ServerName $Selection
        if ($pingResult.IsOnline) {
            Write-Host "✓ Server online ($($pingResult.ResponseTime)ms)" -ForegroundColor Green

            # Detaillierte Informationen
            try {
                Write-Host ""
                Write-Host "Lade Systeminformationen..." -ForegroundColor Gray

                $detailedInfo = Invoke-Command -ComputerName $Selection -Credential $credentials -ScriptBlock {
                    # Verwende WMI für bessere Kompatibilität
                    $os = Get-WmiObject -Class Win32_OperatingSystem -ErrorAction Stop
                    $computer = Get-WmiObject -Class Win32_ComputerSystem -ErrorAction Stop
                    $disks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object DriveType -eq 3
                    $services = Get-Service | Where-Object { $_.Status -eq 'Stopped' -and $_.StartType -eq 'Automatic' } | Select-Object -First 5

                    return @{
                        OSName = $os.Caption
                        OSVersion = $os.Version
                        LastBootTime = $os.ConvertToDateTime($os.LastBootUpTime)
                        TotalMemory = [math]::Round($computer.TotalPhysicalMemory / 1GB, 1)
                        FreeMemory = [math]::Round($os.FreePhysicalMemory / 1KB / 1MB, 0)
                        Manufacturer = $computer.Manufacturer
                        Model = $computer.Model
                        Disks = $disks | ForEach-Object {
                            @{
                                Drive = $_.DeviceID
                                FreeSpace = [math]::Round($_.FreeSpace / 1GB, 1)
                                TotalSpace = [math]::Round($_.Size / 1GB, 1)
                                UsedPercent = [math]::Round((($_.Size - $_.FreeSpace) / $_.Size) * 100, 1)
                            }
                        }
                        StoppedServices = $services | Select-Object Name, DisplayName
                    }
                } -ErrorAction Stop

                Write-Host ""
                Write-Host "SYSTEM" -ForegroundColor White
                Write-Host "──────" -ForegroundColor Gray
                Write-Host "OS: $($detailedInfo.OSName)" -ForegroundColor Gray
                Write-Host "Hardware: $($detailedInfo.Manufacturer) $($detailedInfo.Model)" -ForegroundColor Gray

                $uptime = (Get-Date) - $detailedInfo.LastBootTime
                Write-Host "Uptime: $($uptime.Days)d $($uptime.Hours)h $($uptime.Minutes)m" -ForegroundColor Gray
                Write-Host "RAM: $($detailedInfo.FreeMemory)MB frei / $($detailedInfo.TotalMemory)GB gesamt" -ForegroundColor Gray

                Write-Host ""
                Write-Host "FESTPLATTEN" -ForegroundColor White
                Write-Host "───────────" -ForegroundColor Gray
                foreach ($disk in $detailedInfo.Disks) {
                    $color = if ($disk.UsedPercent -gt 85) { 'Red' } elseif ($disk.UsedPercent -gt 75) { 'Yellow' } else { 'Gray' }
                    Write-Host "$($disk.Drive) $($disk.FreeSpace)GB frei / $($disk.TotalSpace)GB ($($disk.UsedPercent)%)" -ForegroundColor $color
                }

                if ($detailedInfo.StoppedServices.Count -gt 0) {
                    Write-Host ""
                    Write-Host "GESTOPPTE SERVICES" -ForegroundColor Yellow
                    Write-Host "──────────────────" -ForegroundColor Gray
                    foreach ($service in $detailedInfo.StoppedServices) {
                        Write-Host "✗ $($service.Name)" -ForegroundColor Yellow
                    }
                }
            }
            catch {
                Write-Host ""
                Write-Host "✗ Detaillierte Informationen nicht verfügbar" -ForegroundColor Red
                Write-Host "  WinRM/PowerShell Remoting Problem" -ForegroundColor Gray
            }
        } else {
            Write-Host "✗ Server offline" -ForegroundColor Red
        }
    }

    Write-Host "`nDrücken Sie eine beliebige Taste zum Fortfahren..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Hauptprogramm
function Start-ServerManagement {
    Show-Header
    
    if (-not $NoInteractive) {
        do {
            Show-MainMenu
            $choice = Read-Host "Ihre Auswahl (1-9)"
            
            switch ($choice) {
                "1" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-PingServers -Selection $selection
                    }
                }
                "2" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-SearchUpdates -Selection $selection
                    }
                }
                "3" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-InstallUpdates -Selection $selection
                    }
                }
                "4" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-RestartServers -Selection $selection
                    }
                }
                "5" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-ShowServerStatus -Selection $selection
                    }
                }
                "6" {
                    Write-Host "Erweiterte Funktionen (in Entwicklung)" -ForegroundColor Gray
                    Start-Sleep -Seconds 1
                }
                "7" {
                    Write-Host "Einstellungen (in Entwicklung)" -ForegroundColor Gray
                    Start-Sleep -Seconds 1
                }
                "8" {
                    Write-Host "Berichte (in Entwicklung)" -ForegroundColor Gray
                    Start-Sleep -Seconds 1
                }
                "9" {
                    Write-Host ""
                    Write-Host "Auf Wiedersehen!" -ForegroundColor White
                    Send-ToastNotification -Title "Server Management" -Message "Sitzung beendet" -Type Info
                    return
                }
                default {
                    Write-Host "Ungültige Auswahl" -ForegroundColor Red
                    Start-Sleep -Seconds 1
                }
            }
        } while ($true)
    }
}

# Skript starten
Start-ServerManagement
