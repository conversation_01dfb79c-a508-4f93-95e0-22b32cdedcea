#Requires -Version 7.0
<#
.SYNOPSIS
    Modernes Windows Server 2022 Verwaltungsskript
.DESCRIPTION
    Modernisiertes PowerShell-Skript für die zuverlässige Verwaltung von Windows Server 2022
    von einem Windows 11 Client aus. Bietet erweiterte Funktionen für Updates, Neustarts,
    Monitoring und Benachrichtigungen.
.AUTHOR
    Tonino Gerns
.VERSION
    2.0.0
.NOTES
    Erfordert PowerShell 7.0+ und Administratorrechte
#>

[CmdletBinding()]
param(
    [string]$ConfigPath = ".\Config\ServerConfig.psd1",
    [switch]$NoInteractive
)

# Prüfe PowerShell Version
if ($PSVersionTable.PSVersion.Major -lt 7) {
    Write-Error "Dieses Skript erfordert PowerShell 7.0 oder höher. Aktuelle Version: $($PSVersionTable.PSVersion)"
    exit 1
}

# Hinweis: Skript läuft als normaler Benutzer, Credentials werden für Remote-Zugriff verwendet
Write-Host "ℹ️  Dieses Skript läuft als normaler Benutzer. Administrative Aufgaben werden über Remote-Credentials ausgeführt." -ForegroundColor Cyan

# ASCII Header
function Show-Header {
    Clear-Host
    Write-Host @"

██╗    ██╗██╗███╗   ██╗██████╗  ██████╗ ██╗    ██╗███████╗    ███████╗███████╗██████╗ ██╗   ██╗███████╗██████╗ 
██║    ██║██║████╗  ██║██╔══██╗██╔═══██╗██║    ██║██╔════╝    ██╔════╝██╔════╝██╔══██╗██║   ██║██╔════╝██╔══██╗
██║ █╗ ██║██║██╔██╗ ██║██║  ██║██║   ██║██║ █╗ ██║███████╗    ███████╗█████╗  ██████╔╝██║   ██║█████╗  ██████╔╝
██║███╗██║██║██║╚██╗██║██║  ██║██║   ██║██║███╗██║╚════██║    ╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██╔══╝  ██╔══██╗
╚███╔███╔╝██║██║ ╚████║██████╔╝╚██████╔╝╚███╔███╔╝███████║    ███████║███████╗██║  ██║ ╚████╔╝ ███████╗██║  ██║
 ╚══╝╚══╝ ╚═╝╚═╝  ╚═══╝╚═════╝  ╚═════╝  ╚══╝╚══╝ ╚══════╝    ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝
                                                                                                                   
███╗   ███╗ █████╗ ███╗   ██╗ █████╗  ██████╗ ███████╗███╗   ███╗███████╗███╗   ██╗████████╗    ██╗   ██╗██████╗ 
████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔════╝ ██╔════╝████╗ ████║██╔════╝████╗  ██║╚══██╔══╝    ██║   ██║╚════██╗
██╔████╔██║███████║██╔██╗ ██║███████║██║  ███╗█████╗  ██╔████╔██║█████╗  ██╔██╗ ██║   ██║       ██║   ██║ █████╔╝
██║╚██╔╝██║██╔══██║██║╚██╗██║██╔══██║██║   ██║██╔══╝  ██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║       ╚██╗ ██╔╝██╔═══╝ 
██║ ╚═╝ ██║██║  ██║██║ ╚████║██║  ██║╚██████╔╝███████╗██║ ╚═╝ ██║███████╗██║ ╚████║   ██║        ╚████╔╝ ███████╗
╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝         ╚═══╝  ╚══════╝

"@ -ForegroundColor Cyan
    
    Write-Host "Modernes Windows Server Management v2.0" -ForegroundColor Green
    Write-Host "Entwickelt für Windows Server 2022 & Windows 11" -ForegroundColor Green
    Write-Host "by Tonino Gerns - $(Get-Date -Format 'dd.MM.yyyy')" -ForegroundColor Gray
    Write-Host ""
}

# Module importieren
try {
    $modulePath = Join-Path $PSScriptRoot "Modules\ServerManagement.psm1"
    if (-not (Test-Path $modulePath)) {
        throw "ServerManagement.psm1 nicht gefunden in: $modulePath"
    }

    Import-Module $modulePath -Force
    Write-Host "[✓] ServerManagement Modul erfolgreich geladen" -ForegroundColor Green

    # Erweiterte Features laden (optional)
    $extendedPath = Join-Path $PSScriptRoot "Modules\ExtendedFeatures.psm1"
    if (Test-Path $extendedPath) {
        try {
            Import-Module $extendedPath -Force
            Write-Host "[✓] Erweiterte Features geladen" -ForegroundColor Green
        }
        catch {
            Write-Host "[⚠] Erweiterte Features konnten nicht geladen werden: $_" -ForegroundColor Yellow
        }
    }
}
catch {
    Write-Error "Fehler beim Laden der Module: $_"
    exit 1
}

# Konfiguration laden
try {
    if (-not (Test-Path $ConfigPath)) {
        throw "Konfigurationsdatei nicht gefunden: $ConfigPath"
    }
    
    $config = Import-ServerConfig -ConfigPath $ConfigPath
    Write-Host "[✓] Konfiguration erfolgreich geladen" -ForegroundColor Green
}
catch {
    Write-Error "Fehler beim Laden der Konfiguration: $_"
    exit 1
}

# Hauptmenü
function Show-MainMenu {
    Write-Host "`n=== HAUPTMENÜ ===" -ForegroundColor Cyan
    Write-Host "1.  🌐 Alle Server anpingen" -ForegroundColor Yellow
    Write-Host "2.  🔍 Windows Updates suchen" -ForegroundColor Yellow  
    Write-Host "3.  📦 Windows Updates installieren" -ForegroundColor Yellow
    Write-Host "4.  🔄 Server neu starten" -ForegroundColor Yellow
    Write-Host "5.  📊 Server-Status anzeigen" -ForegroundColor Yellow
    Write-Host "6.  🛠️  Erweiterte Funktionen" -ForegroundColor Yellow
    Write-Host "7.  ⚙️  Einstellungen" -ForegroundColor Yellow
    Write-Host "8.  📋 Berichte anzeigen" -ForegroundColor Yellow
    Write-Host "9.  ❌ Beenden" -ForegroundColor Red
    Write-Host ""
}

# Server-Auswahlmenü
function Show-ServerSelectionMenu {
    Write-Host "`n=== SERVER AUSWAHL ===" -ForegroundColor Cyan
    Write-Host "1. 🌐 Alle Server" -ForegroundColor Yellow
    Write-Host "2. 🖥️  Einzelnen Server auswählen" -ForegroundColor Yellow
    Write-Host "3. 📋 Server-Liste anzeigen" -ForegroundColor Yellow
    Write-Host "4. ⬅️  Zurück zum Hauptmenü" -ForegroundColor Gray
    Write-Host ""
    
    $choice = Read-Host "Ihre Auswahl (1-4)"
    
    switch ($choice) {
        "1" { return "all" }
        "2" { 
            Show-ServerList
            $serverName = Read-Host "Servernamen eingeben"
            return $serverName
        }
        "3" { 
            Show-ServerList
            return Show-ServerSelectionMenu
        }
        "4" { return "back" }
        default { 
            Write-Host "Ungültige Auswahl!" -ForegroundColor Red
            return Show-ServerSelectionMenu
        }
    }
}

function Show-ServerList {
    Write-Host "`n=== VERFÜGBARE SERVER ===" -ForegroundColor Cyan
    $config.ServerList | ForEach-Object -Begin { $i = 1 } -Process {
        Write-Host "$i. $($_.Name) ($($_.Role))" -ForegroundColor White
        $i++
    }
    Write-Host ""
}

# Ping-Funktion
function Invoke-PingServers {
    param([string]$Selection)
    
    Write-Host "`n🌐 Starte Server-Konnektivitätstest..." -ForegroundColor Cyan
    
    if ($Selection -eq "all") {
        $results = Test-AllServers
    } else {
        $result = Test-ServerConnectivity -ServerName $Selection
        $results = @($result)
    }
    
    # Toast-Benachrichtigung
    $onlineCount = ($results | Where-Object IsOnline).Count
    $totalCount = $results.Count
    Send-ToastNotification -Title "Server Ping Abgeschlossen" -Message "$onlineCount von $totalCount Servern sind online" -Type Success
    
    Write-Host "`nDrücken Sie eine beliebige Taste zum Fortfahren..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Windows Update Suche
function Invoke-SearchUpdates {
    param([string]$Selection)
    
    Write-Host "`n🔍 Suche nach Windows Updates..." -ForegroundColor Cyan
    
    $credentials = Get-ServerCredentials
    
    if ($Selection -eq "all") {
        $serverList = $config.ServerList
        
        if ($config.Features.ParallelProcessing) {
            Write-Host "Verwende parallele Verarbeitung..." -ForegroundColor Green
            
            $results = $serverList | ForEach-Object -Parallel {
                $server = $_
                $cred = $using:credentials

                $modulePath = Join-Path $using:PSScriptRoot "Modules\ServerManagement.psm1"
                Import-Module $modulePath -Force

                try {
                    $updates = Search-WindowsUpdatesModern -ServerName $server.Name -Credential $cred
                    return @{
                        ServerName = $server.Name
                        Updates = $updates
                        Success = $true
                    }
                }
                catch {
                    return @{
                        ServerName = $server.Name
                        Error = $_.Exception.Message
                        Success = $false
                    }
                }
            } -ThrottleLimit $config.Features.MaxParallelJobs
        } else {
            $results = @()
            foreach ($server in $serverList) {
                try {
                    $updates = Search-WindowsUpdatesModern -ServerName $server.Name -Credential $credentials
                    $results += @{
                        ServerName = $server.Name
                        Updates = $updates
                        Success = $true
                    }
                }
                catch {
                    $results += @{
                        ServerName = $server.Name
                        Error = $_.Exception.Message
                        Success = $false
                    }
                }
            }
        }
        
        # Ergebnisse anzeigen
        foreach ($result in $results) {
            if ($result.Success) {
                Write-Host "`n📊 Server: $($result.ServerName)" -ForegroundColor Green
                if ($result.Updates.Count -gt 0) {
                    $result.Updates | Format-Table -AutoSize Title, Size, Categories, KBArticleIDs
                } else {
                    Write-Host "  ✅ Keine Updates verfügbar" -ForegroundColor Green
                }
            } else {
                Write-Host "`n❌ Server: $($result.ServerName) - Fehler: $($result.Error)" -ForegroundColor Red
            }
        }
    } else {
        try {
            $updates = Search-WindowsUpdatesModern -ServerName $Selection -Credential $credentials
            Write-Host "`n📊 Updates für $Selection" -ForegroundColor Green
            if ($updates.Count -gt 0) {
                $updates | Format-Table -AutoSize Title, Size, Categories, KBArticleIDs
            } else {
                Write-Host "  ✅ Keine Updates verfügbar" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "❌ Fehler bei $Selection`: $_" -ForegroundColor Red
        }
    }
    
    Write-Host "`nDrücken Sie eine beliebige Taste zum Fortfahren..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Windows Update Installation
function Invoke-InstallUpdates {
    param([string]$Selection)

    Write-Host "`n📦 Installiere Windows Updates..." -ForegroundColor Cyan
    Write-Host "⚠️  WICHTIG: Updates werden OHNE automatischen Neustart installiert!" -ForegroundColor Yellow

    $confirm = Read-Host "Möchten Sie fortfahren? (j/n)"
    if ($confirm -ne "j") {
        Write-Host "Vorgang abgebrochen." -ForegroundColor Yellow
        return
    }

    $credentials = Get-ServerCredentials

    if ($Selection -eq "all") {
        $serverList = $config.ServerList

        foreach ($server in $serverList) {
            Write-Host "`n🔄 Installiere Updates auf $($server.Name)..." -ForegroundColor Cyan
            try {
                $result = Install-WindowsUpdatesModern -ServerName $server.Name -Credential $credentials -NoReboot

                if ($result.Status -eq "Success") {
                    Write-Host "✅ $($result.UpdatesInstalled) Updates installiert auf $($server.Name)" -ForegroundColor Green
                    if ($result.RebootRequired) {
                        Write-Host "⚠️  Neustart erforderlich für $($server.Name)" -ForegroundColor Yellow
                    }
                } elseif ($result.Status -eq "NoUpdates") {
                    Write-Host "ℹ️  Keine Updates verfügbar für $($server.Name)" -ForegroundColor Blue
                }
            }
            catch {
                Write-Host "❌ Fehler bei $($server.Name): $_" -ForegroundColor Red
            }
        }

        Send-ToastNotification -Title "Update Installation" -Message "Updates auf allen Servern abgeschlossen" -Type Success
    } else {
        Write-Host "`n🔄 Installiere Updates auf $Selection..." -ForegroundColor Cyan
        try {
            $result = Install-WindowsUpdatesModern -ServerName $Selection -Credential $credentials -NoReboot

            if ($result.Status -eq "Success") {
                Write-Host "✅ $($result.UpdatesInstalled) Updates installiert" -ForegroundColor Green
                if ($result.RebootRequired) {
                    Write-Host "⚠️  Neustart erforderlich" -ForegroundColor Yellow
                }
                Send-ToastNotification -Title "Update Installation" -Message "$($result.UpdatesInstalled) Updates auf $Selection installiert" -Type Success
            } elseif ($result.Status -eq "NoUpdates") {
                Write-Host "ℹ️  Keine Updates verfügbar" -ForegroundColor Blue
            }
        }
        catch {
            Write-Host "❌ Fehler: $_" -ForegroundColor Red
        }
    }

    Write-Host "`nDrücken Sie eine beliebige Taste zum Fortfahren..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Server Neustart
function Invoke-RestartServers {
    param([string]$Selection)

    Write-Host "`n🔄 Server Neustart..." -ForegroundColor Cyan

    if ($Selection -eq "all") {
        Write-Host "⚠️  ACHTUNG: Alle Server werden in der korrekten Reihenfolge neu gestartet!" -ForegroundColor Red
        Write-Host "Reihenfolge: DC01 → DC02 → DB01 → APP01 → MAIL → FILE → TS01" -ForegroundColor Yellow

        $confirm = Read-Host "Sind Sie sicher? (JA/nein)"
        if ($confirm -ne "JA") {
            Write-Host "Vorgang abgebrochen." -ForegroundColor Yellow
            return
        }

        $credentials = Get-ServerCredentials

        Write-Host "`n🚀 Starte geordneten Neustart aller Server..." -ForegroundColor Cyan
        $results = Restart-AllServersOrdered -Credential $credentials

        # Ergebnisse anzeigen
        Write-Host "`n📊 NEUSTART-ERGEBNISSE:" -ForegroundColor Cyan
        foreach ($result in $results) {
            if ($result.RestartSuccessful) {
                Write-Host "✅ $($result.ServerName) - Erfolgreich ($('{0:F1}' -f $result.TotalTime)s)" -ForegroundColor Green
            } else {
                Write-Host "❌ $($result.ServerName) - Fehler: $($result.Error)" -ForegroundColor Red
            }
        }

        $successful = ($results | Where-Object RestartSuccessful).Count
        $total = $results.Count
        Send-ToastNotification -Title "Server Neustart" -Message "$successful von $total Servern erfolgreich neu gestartet" -Type Success

    } else {
        Write-Host "⚠️  Server $Selection wird neu gestartet!" -ForegroundColor Yellow
        $confirm = Read-Host "Fortfahren? (j/n)"
        if ($confirm -ne "j") {
            Write-Host "Vorgang abgebrochen." -ForegroundColor Yellow
            return
        }

        $credentials = Get-ServerCredentials

        Write-Host "`n🔄 Starte Neustart von $Selection..." -ForegroundColor Cyan
        $result = Restart-ServerSafe -ServerName $Selection -Credential $credentials

        if ($result.RestartSuccessful) {
            Write-Host "✅ $Selection erfolgreich neu gestartet ($('{0:F1}' -f $result.TotalTime)s)" -ForegroundColor Green
            Send-ToastNotification -Title "Server Neustart" -Message "$Selection erfolgreich neu gestartet" -Type Success
        } else {
            Write-Host "❌ Fehler beim Neustart von $Selection`: $($result.Error)" -ForegroundColor Red
        }
    }

    Write-Host "`nDrücken Sie eine beliebige Taste zum Fortfahren..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Server Status anzeigen
function Invoke-ShowServerStatus {
    param([string]$Selection)

    Write-Host "`n📊 Server-Status wird abgerufen..." -ForegroundColor Cyan

    $credentials = Get-ServerCredentials

    if ($Selection -eq "all") {
        $serverList = $config.ServerList

        Write-Host "`n=== SERVER STATUS ÜBERSICHT ===" -ForegroundColor Cyan

        foreach ($server in $serverList) {
            Write-Host "`n🖥️  Server: $($server.Name) ($($server.Role))" -ForegroundColor White

            # Ping Test
            $pingResult = Test-ServerConnectivity -ServerName $server.Name
            if ($pingResult.IsOnline) {
                Write-Host "  🌐 Online ($('{0}ms' -f $pingResult.ResponseTime))" -ForegroundColor Green

                # Zusätzliche Informationen abrufen
                try {
                    $systemInfo = Invoke-Command -ComputerName $server.Name -Credential $credentials -ScriptBlock {
                        $os = Get-CimInstance -ClassName Win32_OperatingSystem
                        $computer = Get-CimInstance -ClassName Win32_ComputerSystem
                        $disk = Get-CimInstance -ClassName Win32_LogicalDisk | Where-Object DeviceID -eq "C:"

                        return @{
                            LastBootTime = $os.LastBootUpTime
                            TotalMemory = [math]::Round($computer.TotalPhysicalMemory / 1GB, 2)
                            FreeMemory = [math]::Round($os.FreePhysicalMemory / 1MB, 0)
                            DiskFreeSpace = [math]::Round($disk.FreeSpace / 1GB, 2)
                            DiskTotalSpace = [math]::Round($disk.Size / 1GB, 2)
                            CPUUsage = (Get-Counter '\Processor(_Total)\% Processor Time').CounterSamples.CookedValue
                        }
                    } -ErrorAction Stop

                    $uptime = (Get-Date) - $systemInfo.LastBootTime
                    Write-Host "  ⏱️  Uptime: $($uptime.Days)d $($uptime.Hours)h $($uptime.Minutes)m" -ForegroundColor Blue
                    Write-Host "  💾 RAM: $($systemInfo.FreeMemory)MB frei von $($systemInfo.TotalMemory)GB" -ForegroundColor Blue
                    Write-Host "  💿 Disk C: $($systemInfo.DiskFreeSpace)GB frei von $($systemInfo.DiskTotalSpace)GB" -ForegroundColor Blue

                    # Warnung bei wenig Speicherplatz
                    $diskUsagePercent = (($systemInfo.DiskTotalSpace - $systemInfo.DiskFreeSpace) / $systemInfo.DiskTotalSpace) * 100
                    if ($diskUsagePercent -gt (100 - $config.Settings.DiskSpaceThreshold)) {
                        Write-Host "  ⚠️  WARNUNG: Wenig Speicherplatz!" -ForegroundColor Red
                    }
                }
                catch {
                    Write-Host "  ❌ Fehler beim Abrufen der Systeminformationen: $_" -ForegroundColor Red
                }
            } else {
                Write-Host "  🔴 Offline" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "`n🖥️  Detaillierter Status für: $Selection" -ForegroundColor White

        $pingResult = Test-ServerConnectivity -ServerName $Selection
        if ($pingResult.IsOnline) {
            Write-Host "🌐 Server ist online ($('{0}ms' -f $pingResult.ResponseTime))" -ForegroundColor Green

            # Detaillierte Informationen
            try {
                Write-Host "`nSysteminformationen werden abgerufen..." -ForegroundColor Cyan

                $detailedInfo = Invoke-Command -ComputerName $Selection -Credential $credentials -ScriptBlock {
                    $os = Get-CimInstance -ClassName Win32_OperatingSystem
                    $computer = Get-CimInstance -ClassName Win32_ComputerSystem
                    $disks = Get-CimInstance -ClassName Win32_LogicalDisk | Where-Object DriveType -eq 3
                    $services = Get-Service | Where-Object Status -eq 'Stopped' | Where-Object StartType -eq 'Automatic'

                    return @{
                        OSName = $os.Caption
                        OSVersion = $os.Version
                        LastBootTime = $os.LastBootTime
                        TotalMemory = [math]::Round($computer.TotalPhysicalMemory / 1GB, 2)
                        FreeMemory = [math]::Round($os.FreePhysicalMemory / 1MB, 0)
                        Manufacturer = $computer.Manufacturer
                        Model = $computer.Model
                        Disks = $disks | ForEach-Object {
                            @{
                                Drive = $_.DeviceID
                                FreeSpace = [math]::Round($_.FreeSpace / 1GB, 2)
                                TotalSpace = [math]::Round($_.Size / 1GB, 2)
                                UsedPercent = [math]::Round((($_.Size - $_.FreeSpace) / $_.Size) * 100, 1)
                            }
                        }
                        StoppedServices = $services | Select-Object Name, DisplayName
                    }
                }

                Write-Host "`n=== SYSTEM DETAILS ===" -ForegroundColor Cyan
                Write-Host "OS: $($detailedInfo.OSName) ($($detailedInfo.OSVersion))" -ForegroundColor White
                Write-Host "Hardware: $($detailedInfo.Manufacturer) $($detailedInfo.Model)" -ForegroundColor White

                $uptime = (Get-Date) - $detailedInfo.LastBootTime
                Write-Host "Uptime: $($uptime.Days) Tage, $($uptime.Hours) Stunden, $($uptime.Minutes) Minuten" -ForegroundColor White
                Write-Host "RAM: $($detailedInfo.FreeMemory)MB frei von $($detailedInfo.TotalMemory)GB gesamt" -ForegroundColor White

                Write-Host "`n=== FESTPLATTEN ===" -ForegroundColor Cyan
                foreach ($disk in $detailedInfo.Disks) {
                    $color = if ($disk.UsedPercent -gt 85) { 'Red' } elseif ($disk.UsedPercent -gt 75) { 'Yellow' } else { 'Green' }
                    Write-Host "$($disk.Drive) $($disk.FreeSpace)GB frei von $($disk.TotalSpace)GB ($($disk.UsedPercent)% belegt)" -ForegroundColor $color
                }

                if ($detailedInfo.StoppedServices.Count -gt 0) {
                    Write-Host "`n=== GESTOPPTE AUTOMATISCHE SERVICES ===" -ForegroundColor Yellow
                    $detailedInfo.StoppedServices | Format-Table -AutoSize Name, DisplayName
                }
            }
            catch {
                Write-Host "❌ Fehler beim Abrufen der detaillierten Informationen: $_" -ForegroundColor Red
            }
        } else {
            Write-Host "🔴 Server ist offline" -ForegroundColor Red
        }
    }

    Write-Host "`nDrücken Sie eine beliebige Taste zum Fortfahren..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# Hauptprogramm
function Start-ServerManagement {
    Show-Header
    
    if (-not $NoInteractive) {
        do {
            Show-MainMenu
            $choice = Read-Host "Ihre Auswahl (1-9)"
            
            switch ($choice) {
                "1" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-PingServers -Selection $selection
                    }
                }
                "2" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-SearchUpdates -Selection $selection
                    }
                }
                "3" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-InstallUpdates -Selection $selection
                    }
                }
                "4" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-RestartServers -Selection $selection
                    }
                }
                "5" {
                    $selection = Show-ServerSelectionMenu
                    if ($selection -ne "back") {
                        Invoke-ShowServerStatus -Selection $selection
                    }
                }
                "6" {
                    Write-Host "Erweiterte Funktionen werden implementiert..." -ForegroundColor Yellow
                    Start-Sleep -Seconds 2
                }
                "7" {
                    Write-Host "Einstellungen werden implementiert..." -ForegroundColor Yellow
                    Start-Sleep -Seconds 2
                }
                "8" {
                    Write-Host "Berichte werden implementiert..." -ForegroundColor Yellow
                    Start-Sleep -Seconds 2
                }
                "9" {
                    Write-Host "`n👋 Auf Wiedersehen!" -ForegroundColor Green
                    Send-ToastNotification -Title "Server Management" -Message "Sitzung beendet" -Type Info
                    return
                }
                default {
                    Write-Host "❌ Ungültige Auswahl! Bitte wählen Sie 1-9." -ForegroundColor Red
                    Start-Sleep -Seconds 2
                }
            }
        } while ($true)
    }
}

# Skript starten
Start-ServerManagement
