# Windows Server Management v2.0

Modernes PowerShell-Skript für die zuverlässige Verwaltung von Windows Server 2022 von einem Windows 11 Client aus.

## 🚀 Features

### Kernfunktionen
- **🌐 Server-Konnektivität**: Moderne Ping-Tests mit paralleler Verarbeitung
- **🔍 Windows Update Suche**: Native COM-Objekte statt veralteter Module
- **📦 Update Installation**: Sichere Installation ohne automatischen Neustart
- **🔄 Intelligenter Neustart**: Geordneter Neustart in korrekter Reihenfolge
- **📊 Server-Monitoring**: Detaillierte Systeminformationen und Status

### Erweiterte Features
- **⚡ Parallele Verarbeitung**: PowerShell 7+ ForEach-Object -Parallel
- **🔔 Toast-Benachrichtigungen**: Windows 11 native Benachrichtigungen
- **📝 Strukturiertes Logging**: Zeitstempel und Kategorien
- **🛡️ Moderne Authentifizierung**: Kerberos statt CredSSP
- **🎯 Retry-Mechanismen**: <PERSON>uste Fehlerbehandlung
- **📈 Progress Bars**: Bessere Benutzererfahrung

## 📋 Systemanforderungen

- **PowerShell 7.0+** (erford<PERSON>lich)
- **Windows 11** (Client)
- **Windows Server 2022** (Zielserver)
- **Administratorrechte** (erforderlich)
- **WinRM aktiviert** auf allen Zielservern

## 🛠️ Installation

1. **PowerShell 7 installieren** (falls nicht vorhanden):
   ```powershell
   winget install Microsoft.PowerShell
   ```

2. **Dateien herunterladen**:
   - `windows_server_verwaltung_modern.ps1`
   - `ServerManagement.psm1`
   - `ServerConfig.psd1`

3. **Konfiguration anpassen**:
   Bearbeiten Sie `ServerConfig.psd1` und passen Sie die Serverliste an Ihre Umgebung an.

## ⚙️ Konfiguration

### Server-Liste (ServerConfig.psd1)

```powershell
ServerList = @(
    @{
        Name = "DC01"
        Role = "DomainController"
        Priority = 1
        RestartDelay = 180  # 3 Minuten warten nach Neustart
    },
    # ... weitere Server
)
```

### Wichtige Einstellungen

- **Neustart-Reihenfolge**: DC01 → DC02 → DB01 → APP01 → MAIL → FILE → TS01
- **Parallele Verarbeitung**: Standardmäßig aktiviert (4 Jobs)
- **Toast-Benachrichtigungen**: Aktiviert für Windows 11
- **Automatischer Neustart**: Deaktiviert (Sicherheit)

## 🚀 Verwendung

### Skript starten
```powershell
# Als Administrator ausführen
.\windows_server_verwaltung_modern.ps1
```

### Hauptfunktionen

#### 1. 🌐 Server anpingen
- Testet Konnektivität zu allen oder einzelnen Servern
- Zeigt Antwortzeiten und IP-Adressen
- Parallele Verarbeitung für bessere Performance

#### 2. 🔍 Windows Updates suchen
- Moderne COM-basierte Update-Suche
- Kategorisierte Anzeige verfügbarer Updates
- Parallele Suche auf mehreren Servern

#### 3. 📦 Updates installieren
- **WICHTIG**: Installiert Updates OHNE automatischen Neustart
- Zeigt an, welche Server einen Neustart benötigen
- Sichere Installation mit Fehlerbehandlung

#### 4. 🔄 Server neu starten
- **Einzelserver**: Sicherer Neustart mit Überwachung
- **Alle Server**: Geordneter Neustart in korrekter Reihenfolge
- Automatische Online-Überwachung nach Neustart

#### 5. 📊 Server-Status
- Detaillierte Systeminformationen
- RAM, Festplattenspeicher, Uptime
- Warnung bei kritischen Werten
- Gestoppte Services anzeigen

## 🔧 Erweiterte Funktionen

### Parallele Verarbeitung
```powershell
# In ServerConfig.psd1
Features = @{
    ParallelProcessing = $true
    MaxParallelJobs = 4
}
```

### Logging
- Automatisches Logging aller Aktionen
- Farbkodierte Konsolen-Ausgabe
- Strukturierte Log-Dateien

### Benachrichtigungen
- Windows 11 Toast-Benachrichtigungen
- Erfolgs- und Fehlermeldungen
- Zusammenfassungen nach Aktionen

## 🛡️ Sicherheit

### Authentifizierung
- Sichere Credential-Verwaltung
- Keine Passwort-Speicherung
- Moderne Kerberos-Authentifizierung

### Berechtigungen
- Erfordert Administratorrechte
- WinRM-Konfiguration erforderlich
- Sichere Remote-Verbindungen

## 📊 Neustart-Reihenfolge

Die Server werden in folgender Reihenfolge neu gestartet:

1. **DC01** (Domain Controller) - 3 Min Wartezeit
2. **DC02** (Domain Controller) - 2 Min Wartezeit  
3. **DB01** (Database Server) - 3 Min Wartezeit
4. **APP01** (Application Server) - 2 Min Wartezeit
5. **MAIL** (Mail Server) - 2 Min Wartezeit
6. **FILE** (File Server) - 1.5 Min Wartezeit
7. **TS01** (Terminal Server) - Keine Wartezeit

Diese Reihenfolge gewährleistet, dass kritische Infrastrukturdienste zuerst verfügbar sind.

## 🔍 Troubleshooting

### Häufige Probleme

#### PowerShell Version
```powershell
# Version prüfen
$PSVersionTable.PSVersion

# Sollte 7.0 oder höher sein
```

#### WinRM Konfiguration
```powershell
# Auf Zielservern ausführen
Enable-PSRemoting -Force
Set-Item WSMan:\localhost\Client\TrustedHosts -Value "*" -Force
```

#### Firewall
```powershell
# WinRM Firewall-Regel aktivieren
Enable-NetFirewallRule -DisplayGroup "Windows Remote Management"
```

### Log-Dateien
- Standard-Pfad: `C:\Logs\ServerManagement\`
- Dateiname: `ServerManagement_YYYYMMDD.log`
- Aufbewahrung: 30 Tage (konfigurierbar)

## 📝 Changelog

### Version 2.0.0
- ✅ Komplette Neuentwicklung für PowerShell 7+
- ✅ Native Windows Update COM-Objekte
- ✅ Parallele Verarbeitung
- ✅ Toast-Benachrichtigungen
- ✅ Moderne Authentifizierung
- ✅ Strukturiertes Logging
- ✅ Erweiterte Fehlerbehandlung
- ✅ Detailliertes Server-Monitoring

### Version 1.0.0
- Ursprüngliche Version mit PSWindowsUpdate

## 👨‍💻 Entwickler

**Tonino Gerns**
- E-Mail: [Ihre E-Mail]
- Erstellt: 2025
- Lizenz: [Ihre Lizenz]

## 🤝 Beitragen

Verbesserungsvorschläge und Bug-Reports sind willkommen!

## ⚠️ Haftungsausschluss

Dieses Skript wird "wie besehen" bereitgestellt. Testen Sie alle Funktionen in einer Testumgebung, bevor Sie sie in der Produktion verwenden.
