@echo off
title Windows Server Management GUI v2.0
echo.
echo ========================================
echo  Windows Server Management GUI v2.0
echo ========================================
echo.

REM Prüfe ob .NET 8 verfügbar ist
where dotnet >nul 2>nul
if %errorlevel% neq 0 (
    echo FEHLER: .NET 8 ist nicht installiert!
    echo Bitte installieren Sie .NET 8 zuerst:
    echo winget install Microsoft.DotNet.SDK.8
    echo.
    pause
    exit /b 1
)

REM Prüfe .NET Version
for /f "tokens=*" %%i in ('dotnet --version 2^>nul') do set DOTNET_VERSION=%%i
echo .NET Version: %DOTNET_VERSION%

REM Prüfe ob GUI-Anwendung existiert
if exist "%~dp0GUI\publish\ServerManagementApp.exe" (
    echo Starte eigenständige GUI-Anwendung...
    echo.
    "%~dp0GUI\publish\ServerManagementApp.exe"
    goto :end
)

if exist "%~dp0GUI\ServerManagementApp.csproj" (
    echo Kompiliere und starte GUI-Anwendung...
    echo.
    cd /d "%~dp0GUI"
    dotnet run --project ServerManagementApp.csproj
    goto :end
)

echo FEHLER: GUI-Anwendung nicht gefunden!
echo.
echo Mögliche Lösungen:
echo 1. Kompilieren Sie die Anwendung zuerst:
echo    cd GUI
echo    .\build.ps1 -Publish
echo.
echo 2. Oder verwenden Sie das PowerShell-Skript:
echo    .\START-ServerManagement.bat
echo.
pause

:end
if %errorlevel% neq 0 (
    echo.
    echo Ein Fehler ist aufgetreten. Drücken Sie eine Taste zum Beenden...
    pause >nul
)
