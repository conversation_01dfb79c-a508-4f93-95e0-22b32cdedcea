# ASCII Header
Write-Host "" -ForegroundColor Cyan
Write-Host "" -ForegroundColor Cyan
Write-Host "██╗  ██╗ █████╗ ██╗   ██╗ ██████╗     █████╗ ██████╗ ███╗   ███╗██╗███╗   ██╗████████╗ ██████╗  ██████╗ ██╗" -ForegroundColor Cyan
Write-Host "██║  ██║██╔══██╗██║   ██║██╔════╝    ██╔══██╗██╔══██╗████╗ ████║██║████╗  ██║╚══██╔══╝██╔═══██╗██╔═══██╗██║" -ForegroundColor Cyan
Write-Host "███████║███████║██║   ██║██║  ███╗   ███████║██║  ██║██╔████╔██║██║██╔██╗ ██║   ██║   ██║   ██║██║   ██║██║" -ForegroundColor Cyan
Write-Host "██╔══██║██╔══██║██║   ██║██║   ██║   ██╔══██║██║  ██║██║╚██╔╝██║██║██║╚██╗██║   ██║   ██║   ██║██║   ██║██║" -ForegroundColor Cyan
Write-Host "██║  ██║██║  ██║╚██████╔╝╚██████╔╝   ██║  ██║██████╔╝██║ ╚═╝ ██║██║██║ ╚████║   ██║   ╚██████╔╝╚██████╔╝███████╗" -ForegroundColor Cyan
Write-Host "╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝  ╚═════╝    ╚═╝  ╚═╝╚═════╝ ╚═╝     ╚═╝╚═╝╚═╝  ╚═══╝   ╚═╝    ╚═════╝  ╚═════╝ ╚══════╝`n" -ForegroundColor Cyan
Write-Host "by Tonino Gerns - Stand: 14.01.2025" -ForegroundColor Cyan
Write-Host "" -ForegroundColor Cyan
Write-Host "" -ForegroundColor Cyan

# Konfigurierbare Variablen
$configPath = "\\FILE\it\_intern\Inventarisierung\config.psd1"

# Funktion zum Laden der Konfiguration
function Load-Config {
    if (Test-Path $configPath) {
        $config = Import-PowerShellDataFile -Path $configPath
        if (-not $config.SpeicherortInventarisierung) {
            Write-Warning "[!] Speicherort fehlt in der Konfigurationsdatei. Fallback auf Desktop."
            $config.SpeicherortInventarisierung = [Environment]::GetFolderPath("Desktop")
        }
        if (-not $config.LocalAdminUser -or -not $config.DebugPassword) {
            Write-Error "[!] LocalAdminUser oder DebugPassword fehlen in der Konfigurationsdatei. Einige Funktionen werden nicht verfügbar sein."
            $config.ConfigLoadError = $true
        }
    } else {
        Write-Error "[!] Konfigurationsdatei nicht gefunden. Einige Funktionen werden nicht verfügbar sein."
        $config = @{
            SpeicherortInventarisierung = [Environment]::GetFolderPath("Desktop")
            WartezeitInSekunden = 2
            ConfigLoadError = $true
        }
    }
    return $config
}

# Funktion zur Überprüfung der Administratorrechte
function Check-AdminRights {
    if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
        Write-Warning "[!] Dieses Skript muss mit Administratorrechten ausgeführt werden."
        return $false
    }
    return $true
}

# Funktion zum Sammeln von Computerinformationen
function Get-ComputerInformation {
    Write-Host "[+] Computerinformationen werden gesammelt..." -ForegroundColor White
       
    $info = @{
        Hostname = $env:COMPUTERNAME
        OS = (Get-CimInstance -ClassName Win32_OperatingSystem).Caption
        LoggedInUser = ((Get-CimInstance -ClassName Win32_ComputerSystem).UserName).Split('\')[-1]
        Manufacturer = (Get-CimInstance -ClassName Win32_ComputerSystem).Manufacturer
        Model = (Get-CimInstance -ClassName Win32_ComputerSystem).Model
        SerialNumber = (Get-CimInstance -ClassName Win32_BIOS).SerialNumber
        RAM = [math]::Round(((Get-CimInstance -ClassName Win32_ComputerSystem).TotalPhysicalMemory / 1GB), 2)
        AnyDeskID = Get-AnyDeskID
        FreeSpaceC = Get-FreeDiskSpace -DriveLetter "C"
        ESETStatus = Get-ESETStatus
    }

    return $info
}

function Get-AnyDeskID {
    $anyDeskPath = "C:\Program Files (x86)\AnyDesk\AnyDesk.exe"
    if (Test-Path $anyDeskPath) {
        $anyDeskID = & "$anyDeskPath" --get-id | ForEach-Object { $_.Trim() }
        if ($anyDeskID) {
            return $anyDeskID
        } else {
            return "Nicht verfügbar"
        }
    }
    return "Nicht installiert"
}

function Get-FreeDiskSpace {
    param ([string]$DriveLetter)
    $drive = Get-PSDrive -Name $DriveLetter
    $totalSpace = $drive.Used + $drive.Free
    return [math]::Round((($drive.Free / $totalSpace) * 100), 2)
}

function Get-ESETStatus {
    $esetPath = "C:\Program Files\ESET\RemoteAdministrator\Agent\ERAAgent.exe"
    if (Test-Path $esetPath) {
        return "Installiert"
    } else {
        return "Nicht installiert"
    }
}

# Funktion zum Speichern der Computerinformationen
function Save-ComputerInformation {
    $info = Get-ComputerInformation
    $fileName = Join-Path -Path $config.SpeicherortInventarisierung -ChildPath "$($info.Hostname)_$($info.LoggedInUser).csv"
    
    $data = @(
        "Systemname; $($info.Hostname)",
        "Betriebssystem; $($info.OS)",
        "Benutzer; $($info.LoggedInUser)",
        "Hersteller; $($info.Manufacturer)",
        "Modell; $($info.Model)",
        "Seriennummer; $($info.SerialNumber)",
        "RAM (GB); $($info.RAM)",
        "AnyDesk-ID; $($info.AnyDeskID)",
        "Freier Speicher auf C (%); $($info.FreeSpaceC)",
        "ESET Management Agent; $($info.ESETStatus)"
    )
    
    $data | Out-File -FilePath $fileName -Encoding UTF8 -Force
    Start-Sleep -Seconds $config.WartezeitInSekunden
    Write-Host "[✓] Computerinformationen wurden gesammelt und gespeichert in $fileName.`n" -ForegroundColor Green
}

# Funktion zum Erstellen eines Systemwiederherstellungspunkts
function Create-SystemRestorePoint {
    Write-Host "[+] Erstelle Systemwiederherstellungspunkt..." -ForegroundColor White
    Enable-ComputerRestore -Drive "C:\" -ErrorAction SilentlyContinue
    Checkpoint-Computer -Description "Inventarisierung und Wartung" -RestorePointType MODIFY_SETTINGS
    Write-Host "[✓] Systemwiederherstellungspunkt wurde erstellt.`n" -ForegroundColor Green
}

# Funktion zum Überprüfen und Installieren von Windows-Updates
function Check-WindowsUpdates {
    Write-Host "[+] Starte Windows Update-Prozess..." -ForegroundColor White
    Start-Sleep -Seconds $config.WartezeitInSekunden

    # Install PSWindowsUpdate module
    Write-Host "[+] Installiere PSWindowsUpdate Modul..." -ForegroundColor White
    Install-Module -Name PSWindowsUpdate -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds $config.WartezeitInSekunden
    Write-Host "[✓] PSWindowsUpdate Modul wurde erfolgreich installiert." -ForegroundColor Green

    # Set execution policy
    Write-Host "[+] Setze Execution Policy..." -ForegroundColor White
    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
    Start-Sleep -Seconds $config.WartezeitInSekunden
    Write-Host "[✓] Execution Policy wurde gesetzt." -ForegroundColor Green

    # Import PSWindowsUpdate module
    Write-Host "[+] Importiere PSWindowsUpdate Modul..." -ForegroundColor White
    Import-Module PSWindowsUpdate
    Start-Sleep -Seconds $config.WartezeitInSekunden
    Write-Host "[✓] PSWindowsUpdate Modul wurde importiert." -ForegroundColor Green

    # Add Windows Update service manager with automatic confirmation
    Write-Host "[+] Füge Windows Update Service Manager hinzu..." -ForegroundColor White
    Add-WUServiceManager -ServiceID "7971f918-a847-4430-9279-4a52d1efe18d" -AddServiceFlag 7 -Confirm:$false
    Start-Sleep -Seconds $config.WartezeitInSekunden
    Write-Host "[✓] Windows Update Service Manager wurde erfolgreich hinzugefügt." -ForegroundColor Green

    # Get list of available updates
    Write-Host "[+] Suche nach verfügbaren Windows-Updates..." -ForegroundColor White
    Start-Sleep -Seconds $config.WartezeitInSekunden

    $availableUpdates = Get-WUList -MicrosoftUpdate

    if ($availableUpdates.Count -eq 0) {
        Write-Host "[✓] Keine neuen Updates verfügbar.`n" -ForegroundColor Green
    } else {
        Write-Host "[!] $($availableUpdates.Count) Updates gefunden:" -ForegroundColor Yellow
        $availableUpdates | Format-Table -AutoSize -Property KB, Size, Title

        $userChoice = Read-Host "Möchten Sie die Updates installieren? (J/N)"
        if ($userChoice -eq 'J') {
            Write-Host "[+] Installiere Updates..." -ForegroundColor White
            $installResult = Install-WindowsUpdate -MicrosoftUpdate -AcceptAll -IgnoreReboot -Verbose:$false | Out-Null

            Write-Host "[+] Prüfe, ob ein Neustart erforderlich ist..." -ForegroundColor White
            Start-Sleep -Seconds $config.WartezeitInSekunden
            $rebootStatus = Get-WURebootStatus
            if ($rebootStatus.RebootRequired) {
                Write-Host "[!] Ein Neustart ist erforderlich, um die Installation abzuschließen." -ForegroundColor Yellow
            } else {
                Write-Host "[✓] Kein Neustart erforderlich." -ForegroundColor Green
            }

            Write-Host "[✓] Update-Prozess abgeschlossen.`n" -ForegroundColor Green
        } else {
            Write-Host "[✓] Update-Installation übersprungen.`n" -ForegroundColor Yellow
        }
    }
}

# Funktion zur Benutzerverwaltung
function Manage-Users {
    if ($config.ConfigLoadError) {
        Write-Error "[!] Benutzerverwaltung kann nicht gestartet werden, da die Konfiguration nicht vollständig geladen wurde."
        return
    }
    Write-Host "[+] Prüfe lokales Administratorkonto..." -ForegroundColor White
    if (Get-LocalUser -Name "Administrator" -ErrorAction SilentlyContinue) {
        Disable-LocalUser -Name "Administrator"
        $secureAdminPassword = ConvertTo-SecureString -String (New-Guid).Guid -AsPlainText -Force
        Set-LocalUser -Name "Administrator" -Password $secureAdminPassword
        Write-Host "[✓] 'Administrator' wurde deaktiviert und Passwort geändert.`n" -ForegroundColor Green
    }

    $secureLocalAdminPassword = ConvertTo-SecureString -String (New-Guid).Guid -AsPlainText -Force
    if (Get-LocalUser -Name $config.LocalAdminUser -ErrorAction SilentlyContinue) {
        Write-Host "[!] Benutzer $($config.LocalAdminUser) existiert bereits. Passwort wird aktualisiert." -ForegroundColor Yellow
        Set-LocalUser -Name $config.LocalAdminUser -Password $secureLocalAdminPassword
    } else {
        Write-Host "[+] Erstelle Benutzer $($config.LocalAdminUser)..." -ForegroundColor White
        New-LocalUser -Name $config.LocalAdminUser -Password $secureLocalAdminPassword -FullName "Lokaler Administrator" -Description "Lokaler Administrator für Wartungszwecke"
        Add-LocalGroupMember -Group "Administratoren" -Member $config.LocalAdminUser
        Write-Host "[✓] Benutzer $($config.LocalAdminUser) wurde erstellt und der Administratorengruppe hinzugefügt.`n" -ForegroundColor Green
    }

    Write-Host "[+] Suche nach lokalen Benutzerkonten..." -ForegroundColor White
    $localUsers = Get-LocalUser | Where-Object { $_.Name -notin @("Administrator", $config.LocalAdminUser) }
    Start-Sleep -Seconds $config.WartezeitInSekunden
    $i = 1
    foreach ($user in $localUsers) {
        Write-Output "$i. $($user.Name)"
        $i++
    }
    while ($true) {
        $selection = Read-Host "Geben Sie die Nummer des Benutzers ein, den Sie löschen möchten (oder ENTER zum Beenden)"
        if ([string]::IsNullOrEmpty($selection)) { break }
        if ($selection -as [int] -and $selection -le $localUsers.Count) {
            $userToDelete = $localUsers[$selection - 1].Name
            Remove-LocalUser -Name $userToDelete
            Write-Output "Benutzer $userToDelete wurde gelöscht."
        } else {
            Write-Output "Ungültige Auswahl."
        }
    }

    lusrmgr.msc
}

# Funktion zum Löschen temporärer Dateien
function Clear-TempFiles {
    Write-Host "[+] Lösche temporäre Dateien und Windows-Update-Cache..."  -ForegroundColor White
    Remove-Item -Path $env:TEMP\* -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "C:\Windows\SoftwareDistribution\Download\*" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds $config.WartezeitInSekunden
    Write-Host "[✓] Dateien erfolgreich gelöscht.`n" -ForegroundColor Green
}

# Funktion zur Druckerverwaltung
function Manage-Printers {
    Write-Host "[+] Druckerkonfiguration wird überprüft..." -ForegroundColor White

    $aktuellerBenutzer = (Get-WmiObject -Class Win32_ComputerSystem).UserName
    Write-Host "[✓] Aktuell angemeldeter Benutzer: $aktuellerBenutzer" -ForegroundColor Green

    Start-Sleep -Seconds $config.WartezeitInSekunden

    function Get-UserPrinters {
        $printers = Get-CimInstance -Class Win32_Printer
        return $printers
    }

    function Show-Printers {
        $printers = Get-UserPrinters
        if ($printers.Count -eq 0) {
            Write-Host "Keine Drucker gefunden." -ForegroundColor Yellow
        } else {
            Write-Host "Installierte Drucker:" -ForegroundColor Cyan
            for ($i = 0; $i -lt $printers.Count; $i++) {
                $printerType = if ($printers[$i].Name -like "\\*") { "Netzwerkdrucker" } else { "Lokaler Drucker" }
                Write-Host "$($i + 1). $($printers[$i].Name) ($printerType)"
            }
        }
        Write-Host ""
    }

    function Invoke-AsUser {
        param (
            [string]$Command
        )

        $encodedCommand = [Convert]::ToBase64String([System.Text.Encoding]::Unicode.GetBytes($Command))
        
        # Erstelle eine neue Prozess-Startinfo für PowerShell
        $psi = New-Object System.Diagnostics.ProcessStartInfo
        $psi.FileName = "powershell.exe"
        $psi.Arguments = "-EncodedCommand $encodedCommand"
        $psi.RedirectStandardOutput = $true
        $psi.RedirectStandardError = $true
        $psi.UseShellExecute = $false
        $psi.CreateNoWindow = $true

        # Starte den Prozess als der angemeldete Benutzer
        $process = [System.Diagnostics.Process]::Start($psi)
        $process.WaitForExit()

        $output = $process.StandardOutput.ReadToEnd()
        $error = $process.StandardError.ReadToEnd()

        if ($process.ExitCode -ne 0) {
            Write-Host "Fehler bei der Ausführung des Befehls: $error" -ForegroundColor Red
        }

        return $output
    }

    function Connect-Printer {
        param (
            [string[]]$printerList
        )

        foreach ($printer in $printerList) {
            Write-Host "[+] Drucker $printer wird installiert..." -ForegroundColor White
            $taskName = "AddPrinter_$($printer -replace '[\\:\s]', '_')"
            $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-Command `"(New-Object -ComObject WScript.Network).AddWindowsPrinterConnection('$printer')`""
            $trigger = New-ScheduledTaskTrigger -AtLogOn
            $principal = New-ScheduledTaskPrincipal -UserId $aktuellerBenutzer -LogonType Interactive -RunLevel Limited

            Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Force | Out-Null
            Start-ScheduledTask -TaskName $taskName | Out-Null

            # Wait for a moment to allow the printer to be added
            Start-Sleep -Seconds 5

            # Check if the printer was successfully added
            if (Get-Printer -Name $printer -ErrorAction SilentlyContinue) {
                Write-Host "[✓] Drucker erfolgreich installiert: $printer" -ForegroundColor Green
                # Remove the scheduled task as it's no longer needed
                Unregister-ScheduledTask -TaskName $taskName -Confirm:$false
            } else {
                Write-Host "[!] Fehler beim Installieren des Druckers $printer" -ForegroundColor Red
            }
        }
    }

    function Remove-UserPrinter {
        param (
            [int]$printerIndex
        )
        $printers = Get-UserPrinters
        if ($printerIndex -ge 1 -and $printerIndex -le $printers.Count) {
            $printerToRemove = $printers[$printerIndex - 1].Name
            $taskName = "RemovePrinter_$($printerToRemove -replace '[\\:\s]', '_')"
            $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-Command `"(New-Object -ComObject WScript.Network).RemovePrinterConnection('$printerToRemove')`""
            $trigger = New-ScheduledTaskTrigger -AtLogOn
            $principal = New-ScheduledTaskPrincipal -UserId $aktuellerBenutzer -LogonType Interactive -RunLevel Limited

            Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Force | Out-Null
            Start-ScheduledTask -TaskName $taskName | Out-Null

            # Wait for a moment to allow the printer to be removed
            Start-Sleep -Seconds 5

        } else {
            Write-Host "[!] Ungültige Druckernummer." -ForegroundColor Red
        }
    }

    do {
        Write-Host "`nDruckerverwaltung Optionen:" -ForegroundColor Cyan
        Write-Host "1 - Neue Drucker verbinden" -ForegroundColor Cyan
        Write-Host "2 - Drucker löschen" -ForegroundColor Cyan
        Write-Host "3 - Installierte Drucker anzeigen" -ForegroundColor Cyan
        Write-Host "4 - Beenden" -ForegroundColor Cyan

        $choice = Read-Host "Wählen Sie eine Option (1-4) oder drücken Sie Enter zum Beenden"

        switch ($choice) {
            "1" {
                Write-Host "`nBitte wählen Sie den Standort:" -ForegroundColor White
                Write-Host "1 - Büro" -ForegroundColor Cyan
                Write-Host "2 - Lager" -ForegroundColor Cyan
                Write-Host "3 - IT" -ForegroundColor Cyan

                $standortAuswahl = Read-Host "Ihre Auswahl (1-3)"

                switch ($standortAuswahl) {
                    "1" {
                        $druckerBuero = @(
                            "\\FILE\S_Buero_1OG_Canon",
                            "\\FILE\S_Buero_1OG_Canon (Farbe)",
                            "\\FILE\S_Buero_2OG_Canon",
                            "\\FILE\S_Buero_2OG_Canon (Farbe)"
                        )
                        Connect-Printer -printerList $druckerBuero
                        Write-Host "[✓] Büro-Drucker überprüft und ggf. verbunden.`n" -ForegroundColor Green
                    }
                    "2" {
                        $druckerLager = @(
                            "\\FILE\Lager_AS",
                            "\\FILE\Lager_Etiketten_AS",
                            "\\FILE\Lager_Etiketten_Buero",
                            "\\FILE\Lager_Versandlabel",
                            "\\FILE\S_Logistik_Canon"
                        )
                        Connect-Printer -printerList $druckerLager
                        Write-Host "[✓] Lager-Drucker überprüft und ggf. verbunden.`n" -ForegroundColor Green
                    }
                    "3" {
                        Write-Host "[+] IT-Abteilung: Keine Freigabe-Drucker. Bitte manuell prüfen." -ForegroundColor Yellow
                    }
                    default {
                        Write-Host "[!] Ungültige Eingabe. Vorgang wird abgebrochen." -ForegroundColor Red
                    }
                }
            }
            "2" {
                do {
                    Show-Printers
                    $printerToDelete = Read-Host "Geben Sie die Nummer des zu löschenden Druckers ein (oder drücken Sie Enter zum Beenden)"
                    if ($printerToDelete -eq '') {
                        break
                    }
                    Remove-UserPrinter -printerIndex ([int]$printerToDelete)
                } while ($true)
            }
            "3" {
                Show-Printers
            }
            "4" {
                Write-Host "Druckerverwaltung wird beendet." -ForegroundColor Yellow
                return
            }
            "" {
                Write-Host "Druckerverwaltung wird beendet." -ForegroundColor Yellow
                return
            }
            default {
                Write-Host "[!] Ungültige Auswahl. Bitte versuchen Sie es erneut." -ForegroundColor Red
            }
        }
    } while ($true)
}



# Funktion für den Systemcheck
function Perform-Systemcheck {
    Write-Host "[+] Gruppenrichtlinen werden aktualisiert..." -ForegroundColor White
    gpupdate /force
    Write-Host "[✓] Gruppenrichtlinien erfolgreich aktualisiert.`n" -ForegroundColor Green

    Write-Host "[+] Systemcheck wird durchgeführt..." -ForegroundColor White
    sfc /scannow
    Write-Host "[✓] Systemcheck erfolgreich durchgeführt.`n" -ForegroundColor Green
}

# Funktion zum Ausführen aller Funktionen
function Run-AllFunctions {
    Save-ComputerInformation
    Create-SystemRestorePoint
    Manage-Users
    Clear-TempFiles
    Manage-Printers
    Perform-Systemcheck
    Check-WindowsUpdates	
    Write-Host "[✓] Alle Funktionen wurden ausgeführt.`n" -ForegroundColor Green
}

function Invoke-Debugging {
    if ($config.ConfigLoadError) {
        Write-Error "[!] Debugging kann nicht gestartet werden, da die Konfiguration nicht vollständig geladen wurde."
        return
    }
    $enteredPassword = Read-Host "Bitte geben Sie das Debug-Passwort ein" -AsSecureString
    $enteredPasswordText = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($enteredPassword))
    
    if ($enteredPasswordText -ne $config.DebugPassword) {
        Write-Host "Falsches Passwort. Debugging wird abgebrochen." -ForegroundColor Red
        return
    }

    Write-Host "Debugging-Modus aktiviert. Zeige Variablen und Werte:" -ForegroundColor Yellow

    $debugInfo = [ordered]@{
        "Konfigurationspfad" = $configPath
        "Speicherort Inventarisierung" = $config.SpeicherortInventarisierung
        "Wartezeit in Sekunden" = $config.WartezeitInSekunden
        "Lokaler Admin-Benutzer" = $config.LocalAdminUser
        "AnyDesk-Pfad" = "C:\Program Files (x86)\AnyDesk\AnyDesk.exe"
        "ESET-Pfad" = "C:\Program Files\ESET\RemoteAdministrator\Agent\ERAAgent.exe"
    }

    $debugInfo | Format-Table -AutoSize -Wrap @{L='Variable';E={$_.Name}}, @{L='Wert';E={$_.Value}}

    # Zusätzliche Informationen
    Write-Host "`nZusätzliche Informationen:" -ForegroundColor Yellow
    Write-Host "Betriebssystem: $((Get-CimInstance -ClassName Win32_OperatingSystem).Caption)"
    Write-Host "Freier Speicherplatz auf C: $(Get-FreeDiskSpace -DriveLetter 'C')%"
    Write-Host "AnyDesk-ID: $(Get-AnyDeskID)"
    Write-Host "ESET-Status: $(Get-ESETStatus)"

    Write-Host "`nDebugging abgeschlossen." -ForegroundColor Yellow
}

# Funktion für das Hauptmenü
function Show-ASCII-Header {
    Clear-Host
    Write-Host "" -ForegroundColor Cyan
    Write-Host "" -ForegroundColor Cyan
    Write-Host "██╗  ██╗ █████╗ ██╗   ██╗ ██████╗     █████╗ ██████╗ ███╗   ███╗██╗███╗   ██╗████████╗ ██████╗  ██████╗ ██╗" -ForegroundColor Cyan
    Write-Host "██║  ██║██╔══██╗██║   ██║██╔════╝    ██╔══██╗██╔══██╗████╗ ████║██║████╗  ██║╚══██╔══╝██╔═══██╗██╔═══██╗██║" -ForegroundColor Cyan
    Write-Host "███████║███████║██║   ██║██║  ███╗   ███████║██║  ██║██╔████╔██║██║██╔██╗ ██║   ██║   ██║   ██║██║   ██║██║" -ForegroundColor Cyan
    Write-Host "██╔══██║██╔══██║██║   ██║██║   ██║   ██╔══██║██║  ██║██║╚██╔╝██║██║██║╚██╗██║   ██║   ██║   ██║██║   ██║██║" -ForegroundColor Cyan
    Write-Host "██║  ██║██║  ██║╚██████╔╝╚██████╔╝   ██║  ██║██████╔╝██║ ╚═╝ ██║██║██║ ╚████║   ██║   ╚██████╔╝╚██████╔╝███████╗" -ForegroundColor Cyan
    Write-Host "╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝  ╚═════╝    ╚═╝  ╚═╝╚═════╝ ╚═╝     ╚═╝╚═╝╚═╝  ╚═══╝   ╚═╝    ╚═════╝  ╚═════╝ ╚══════╝`n" -ForegroundColor Cyan
    Write-Host "by Tonino Gerns - Stand: 22.01.2025" -ForegroundColor Cyan
    Write-Host "" -ForegroundColor Cyan
    Write-Host "" -ForegroundColor Cyan
}

function Show-Menu {
    $options = @(
        "Gesamtes Skript ausführen (Standard)",
        "Computerinformationen lesen und speichern",
        "Systemwiederherstellungspunkt erstellen",
        "Windows-Updates prüfen und installieren",
        "Benutzerverwaltung",
        "Temporäre Dateien löschen",
        "Druckerverwaltung",
        "Systemcheck durchführen",
        "Debugging",
        "Skript beenden"
    )

    do {
        Show-ASCII-Header
        Write-Host "Bitte wählen Sie eine Option:" -ForegroundColor White
        for ($i = 0; $i -lt $options.Length; $i++) {
            if ($i -eq 8) {
                Write-Host "99 - $($options[$i])" -ForegroundColor Cyan
            } else {
                Write-Host "$($i + 1) - $($options[$i])" -ForegroundColor Cyan
            }
        }

        $wahl = Read-Host "Ihre Auswahl (1-$($options.Length - 1), 99 oder $($options.Length))"
        if ([string]::IsNullOrWhiteSpace($wahl)) { $wahl = "1" }

        switch ($wahl) {
            { $_ -in "1".."8" } {
                & $functionMap[$_]
                Pause
            }
            "99" {
                Invoke-Debugging
                Pause
            }
            $($options.Length) { 
                Write-Host "[✓] Skript wurde beendet." -ForegroundColor Green
                return $false
            }
            default { 
                Write-Host "[!] Ungültige Auswahl. Bitte erneut versuchen." -ForegroundColor Red 
                Pause
            }
        }
    } while ($true)
}

# Mapping von Menüoptionen zu Funktionen
$functionMap = @{
    "1" = { Run-AllFunctions }
    "2" = { Save-ComputerInformation }
    "3" = { Create-SystemRestorePoint }
    "4" = { Check-WindowsUpdates }
    "5" = { Manage-Users }
    "6" = { Clear-TempFiles }
    "7" = { Manage-Printers }
    "8" = { Perform-Systemcheck }
}

# Hauptprogramm
$config = Load-Config
if (Check-AdminRights) {
    Show-Menu
}

