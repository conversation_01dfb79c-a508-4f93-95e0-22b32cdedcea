# Laden der notwendigen Assembly für Windows-Formulare
Add-Type -AssemblyName System.Windows.Forms

# Verbindung mit Exchange herstellen
$UserCredential = Get-Credential
$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://mail01.haug-components.com/PowerShell/ -Authentication Kerberos -Credential $UserCredential
Import-PSSession $Session -DisableNameChecking


Function Display-MailboxInfo {
    param($Info)

    Write-Host "Informationen für das Postfach: $($Info.Username)" -ForegroundColor Green

    Write-Host "`nE-Mail-Adressen:" -ForegroundColor Yellow
    $Info.EmailAddresses -split "; " | ForEach-Object {
        Write-Host "  $_"
    }

    Write-Host "`nVersteckt in Adresslisten:" -ForegroundColor Yellow
    Write-Host "  $($Info.HiddenFromAddressLists)"

    Write-Host "`nTransportregeln:" -ForegroundColor Yellow
    $Info.TransportRules -split "; " | ForEach-Object {
        Write-Host "  $_"
    }

    Write-Host "`nInbox-Regeln:" -ForegroundColor Yellow
    $Info.InboxRules -split "; " | ForEach-Object {
        Write-Host "  $_"
    }

    Write-Host "`nStellvertreter für den Posteingang:" -ForegroundColor Yellow
    $InboxDelegates = Get-MailboxFolderPermission -Identity ("$($Info.Username):\Posteingang") | Where-Object { $_.User -notlike "Default" -and $_.User -notlike "Anonymous" }
    foreach ($Delegate in $InboxDelegates) {
        Write-Host "  $($Delegate.User) - $($Delegate.AccessRights)"
    }
}


Function Get-MailboxInfo {
    param($Username)

    $Mailbox = Get-Mailbox -Identity $Username

    $InboxDelegates = Get-MailboxFolderPermission -Identity ("$Username" + ":\Posteingang") | Where-Object { $_.User -notlike "Default" -and $_.User -notlike "Anonymous" }

    $DelegateInfo = ""
    foreach ($Delegate in $InboxDelegates) {
        $DelegateInfo += "$($Delegate.User.DisplayName) ($($Delegate.AccessRights)); "
    }
    $DelegateInfo = $DelegateInfo.TrimEnd("; ")

    $Info = [PSCustomObject]@{
        Username                 = $Username
        EmailAddresses           = ($Mailbox.EmailAddresses | Where-Object {$_ -match "SMTP:"} | ForEach-Object {$_ -replace "SMTP:", ""}) -join "; "
        HiddenFromAddressLists   = $Mailbox.HiddenFromAddressListsEnabled
        TransportRules           = (Get-TransportRule | Where-Object { $_.Description -like "*$($Mailbox.PrimarySmtpAddress)*" } | ForEach-Object {$_.Name}) -join "; "
        InboxRules               = (Get-InboxRule -Mailbox $Username | Where-Object { $_.ForwardTo -or $_.RedirectTo } | ForEach-Object {if ($_.ForwardTo) {"$($_.Name): Weiterleiten an $($_.ForwardTo)"} elseif ($_.RedirectTo) {"$($_.Name): Umleiten an $($_.RedirectTo)"}}) -join "; "
        InboxDelegates           = $DelegateInfo
    }

    return $Info
}



do {
    do {
        $Choice = Read-Host "Möchten Sie die Informationen eines einzelnen Benutzers oder aller Benutzer abrufen? (E) Einzeln / (A) Alle"
        $Choice = $Choice.ToUpper()
    } while ($Choice -ne "E" -and $Choice -ne "A")

    $Results = @()

    if ($Choice -eq "E") {
        do {
            $Username = Read-Host "Bitte geben Sie den Benutzernamen des Postfachs ein"
            $Info = Get-MailboxInfo -Username $Username
            Display-MailboxInfo -Info $Info -Username $Username

            $Results += $Info

            do {
                $Continue = Read-Host "Möchten Sie die Informationen eines weiteren Benutzers abrufen? (J) Ja / (N) Nein"
                $Continue = $Continue.ToUpper()
            } while ($Continue -ne "J" -and $Continue -ne "N")

        } while ($Continue -eq "J")
    }
    elseif ($Choice -eq "A") {
        $AllMailboxes = Get-Mailbox -ResultSize Unlimited
        foreach ($Mailbox in $AllMailboxes) {
            $Info = Get-MailboxInfo -Username $Mailbox.UserPrincipalName
            Display-MailboxInfo -Info $Info -Username $Username

            $Results += $Info
        }
    }

    do {
        $SaveToFile = Read-Host "`nMöchten Sie die Informationen in einer Datei speichern? (J) Ja / (N) Nein"
        $SaveToFile = $SaveToFile.ToUpper()
    } while ($SaveToFile -ne "J" -and $SaveToFile -ne "N")

    if ($SaveToFile -eq "J") {
        $SaveFileDialog = New-Object System.Windows.Forms.SaveFileDialog
        $SaveFileDialog.filter = "Excel (*.xlsx)|*.xlsx"
        $SaveFileDialog.ShowDialog()

        if ($SaveFileDialog.FileName) {
            $FilePath = $SaveFileDialog.FileName
            $Results | Export-Excel -Path $FilePath
        }
    }

    do {
        $RunAgain = Read-Host "Möchten Sie das Skript erneut ausführen? (J) Ja / (N) Nein"
        $RunAgain = $RunAgain.ToUpper()
    } while ($RunAgain -ne "J" -and $RunAgain -ne "N")

} while ($RunAgain -eq "J")

# Sitzung beenden
Remove-PSSession $Session
