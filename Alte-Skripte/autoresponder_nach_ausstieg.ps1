# Skriptname: Set-AutoResponder.ps1
# Beschreibung: Die<PERSON> verbindet sich mit einem Exchange 2019-Server und setzt den Autoresponder für einen bestimmten Benutzer.

# Variablen definieren
$Benutzer = "a.hoffmann"

$Anrede = "<PERSON>"
$Name = "<PERSON>"

$AnsprechpartnerGeschlecht = "m" # Geschlecht des Ansprechpartners: "m" oder "w"
$AnsprechpartnerName = "<PERSON> He<PERSON>" 
$AnsprechpartnerEmail = "<EMAIL>" 
$AnsprechpartnerTelefon = "+49 711 13265-16"

$Unternehmen = "Haug Vertriebs GmbH & Co. KG"


# Nachrichtentext basierend auf dem Geschlecht des Ansprechpartners definieren
if ($AnsprechpartnerGeschlecht -eq "m") {
    $Nachricht = @"
Guten Tag,

vielen Dank für Ihre Nachricht. $Anrede $Name ist nicht mehr für unser Unternehmen tätig und kann Ihre E-Mail leider nicht persönlich beantworten. Bitte wenden Sie sich mit Ihrem Anliegen an Herrn $AnsprechpartnerName, der Ihnen per E-Mail an $AnsprechpartnerEmail oder telefonisch unter $AnsprechpartnerTelefon gern weiterhilft. Bitte beachten Sie, dass Ihre E-Mail nicht automatisch weitergeleitet wird.

Mit freundlichen Grüßen
Ihr Team der $Unternehmen
"@
} else {
    $Nachricht = @"
Guten Tag,

vielen Dank für Ihre Nachricht. $Anrede $Name nicht mehr für unser Unternehmen tätig und kann Ihre E-Mail leider nicht persönlich beantworten. Bitte wenden Sie sich mit Ihrem Anliegen an Frau $AnsprechpartnerName, die Ihnen per E-Mail an $AnsprechpartnerEmail oder telefonisch unter $AnsprechpartnerTelefon gern weiterhilft. Bitte beachten Sie, dass Ihre E-Mail nicht automatisch weitergeleitet wird.

Mit freundlichen Grüßen
Ihr Team der $Unternehmen
"@
}

# Anmeldeinformationen anfordern
$UserCredential = Get-Credential

# Verbindung zum Exchange-Server herstellen
$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://mail01.haug-components.com/PowerShell/ -Authentication Kerberos -Credential $UserCredential
Import-PSSession $Session -DisableNameChecking

# Interne und externe automatische Antwort setzen
Set-MailboxAutoReplyConfiguration -Identity $Benutzer -AutoReplyState Enabled -InternalMessage $Nachricht -ExternalMessage $Nachricht -ExternalAudience All

# Sitzung beenden und aufräumen
Remove-PSSession $Session

# Abschlussmeldung
Write-Output "Der Autoresponder wurde erfolgreich für den Benutzer $Benutzer gesetzt."