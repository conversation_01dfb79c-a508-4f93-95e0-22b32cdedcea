# Funktion zum Abrufen der Anmeldeinformationen des Administrators mit Standardbenutzername
function Get-Credentials {
    # Standardbenutzername festlegen
    $username = "edv"
    
    # Benutzereingabeaufforderung für das Passwort
    $password = Read-Host -AsSecureString -Prompt "Bitte Passwort für den Benutzer $username eingeben:"
    
    # Erstellung der Anmeldeinformationen
    $cred = New-Object -TypeName PSCredential -ArgumentList $username, $password
    return $cred
}

# Funktion zum Abrufen der Serverliste
function Get-ServerList {
    return @("DC01", "DC02", "DB01", "APP01", "MAIL01", "FILE", "TS01")
}

# Funktion zum Suchen von Windows-Updates auf einem Remote-Server und Benutzerabfrage zur Installation
function Search-WindowsUpdates {
    param (
        [pscredential]$cred,
        [string]$ServerName
    )
    Write-Output "Suche nach neuesten Windows Updates auf $ServerName..."

    # Führt einen Remote-Befehl aus, um nach Windows Updates zu suchen
    $result = Invoke-Command -ComputerName $ServerName -Credential $cred -ScriptBlock {
        if (-not (Get-Module -ListAvailable -Name PSWindowsUpdate)) {
            Install-Module -Name PSWindowsUpdate -Force -Scope CurrentUser
        }
        Import-Module PSWindowsUpdate
        Get-WindowsUpdate -AcceptAll | Select-Object -Property ComputerName, Status, Size, KB, Title
    } -Authentication Credssp

    $formattedResult = $result | Format-Table -AutoSize | Out-String
    Write-Output $formattedResult

    # Benutzerabfrage für die nächste Aktion
    $action = Read-Host "Möchtest du die gefundenen Updates installieren? Gebe 'ja' oder 'nein' ein"
    if ($action -eq 'ja') {
        Write-Output "Updates werden heruntergeladen und installiert auf $ServerName..."
        Invoke-Command -ComputerName $ServerName -Credential $cred -ScriptBlock {
            if (-not (Get-Module -ListAvailable -Name PSWindowsUpdate)) {
                Install-Module -Name PSWindowsUpdate -Force -Scope CurrentUser
            }
            Import-Module PSWindowsUpdate
            Install-WindowsUpdate -AcceptAll -IgnoreReboot
        } -Authentication Credssp
    } else {
        Write-Output "Keine Aktion durchgeführt auf $ServerName."
    }
}





# Funktion zum Installieren von Windows-Updates auf einem Remote-Server
function Install-WindowsUpdates {
    param (
        [pscredential]$cred,
        [string]$ServerName
    )
    Write-Output "Suchen und Installieren von Windows Updates auf $ServerName..."

    # Führt einen Remote-Befehl aus, um Windows Updates zu installieren
    $result = Invoke-Command -ComputerName $ServerName -Credential $cred -ScriptBlock {
        if (-not (Get-Module -ListAvailable -Name PSWindowsUpdate)) {
            Install-Module -Name PSWindowsUpdate -Force -Scope CurrentUser
        }
        Import-Module PSWindowsUpdate
        Install-WindowsUpdate -AcceptAll -AutoReboot
    }
    $result | Format-Table -AutoSize | Out-String
}

# Funktion zum Abrufen des Update-Status eines Remote-Servers
function Get-WindowsUpdateStatus {
    param (
        [pscredential]$cred,
        [string]$ServerName
    )
    Write-Output "Abrufen des Status der Windows Updates auf $ServerName..."

    # Führt einen Remote-Befehl aus, um den Update-Status abzurufen
    $result = Invoke-Command -ComputerName $ServerName -Credential $cred -ScriptBlock {
        if (-not (Get-Module -ListAvailable -Name PSWindowsUpdate)) {
            Install-Module -Name PSWindowsUpdate -Force -Scope CurrentUser
        }
        Import-Module PSWindowsUpdate
        Get-WUHistory
    }
    $result | Format-Table -AutoSize | Out-String
}

# Funktion zum Neustarten eines Remote-Servers
function Restart-Server {
    param (
        [string]$ServerName,
        [pscredential]$cred
    )
    Write-Output "Neustarten des Servers $ServerName..."

    # Führt einen Remote-Befehl aus, um den Server neu zu starten
    Invoke-Command -ComputerName $ServerName -Credential $cred -ScriptBlock {
        Restart-Computer -Force
    }
}

# Funktion zum Neustarten aller Server in einer bestimmten Reihenfolge
function Restart-AllServers {
    param (
        [pscredential]$cred
    )
    $serversInOrder = Get-ServerList
    foreach ($server in $serversInOrder) {
        Restart-Server -ServerName $server -cred $cred
        if ($server -ne "TS01") {
            Write-Host "Warte 2 Minuten, bevor der nächste Server neu gestartet wird..."
            Start-Sleep -Seconds 120
        }
    }
}

# Funktion zum Anpingen eines Remote-Servers und Abrufen der letzten Startzeit
function Ping-Server {
    param (
        [string]$ServerName,
        [pscredential]$cred
    )
    $ping = Test-Connection -ComputerName $ServerName -Count 1 -ErrorAction SilentlyContinue
    if ($ping) {
        $lastBoot = Invoke-Command -ComputerName $ServerName -Credential $cred -ScriptBlock {
            (Get-CimInstance -ClassName Win32_OperatingSystem).LastBootUpTime
        }
        return [PSCustomObject]@{
            Server     = $ServerName
            Reachable  = "Ja"
            LastBoot   = $lastBoot
        }
    } else {
        return [PSCustomObject]@{
            Server     = $ServerName
            Reachable  = "Nein"
            LastBoot   = "N/A"
        }
    }
}

# Menüfunktion für die Hauptauswahl
function Show-Menu {
    Write-Host "`nWähle eine Aufgabe aus:" -ForegroundColor Cyan
    Write-Host "1. Neuste Windows Updates suchen" -ForegroundColor Yellow
    Write-Host "2. Windows Updates suchen und installieren" -ForegroundColor Yellow
    Write-Host "3. Status der Windows Updates abrufen" -ForegroundColor Yellow
    Write-Host "4. Windows Server neu starten" -ForegroundColor Yellow
    Write-Host "5. Server anpingen" -ForegroundColor Yellow
    Write-Host "6. Skript beenden" -ForegroundColor Red
    $choice = Read-Host "Geben Sie Ihre Wahl ein (1-6)"
    return $choice
}

# Menüfunktion für die Auswahl von Einzel- oder Alle-Server-Aktionen
function ServerSelectionMenu {
    Write-Host "`nMöchtest du die Aktion auf allen Servern oder einem einzelnen Server durchführen?" -ForegroundColor Cyan
    Write-Host "1. Alle Server" -ForegroundColor Yellow
    Write-Host "2. Einzelnen Server" -ForegroundColor Yellow
    $selection = Read-Host "Geben Sie Ihre Wahl ein (1-2)"
    return $selection
}

# Abrufen der Administrator-Zugangsdaten
$credentials = Get-Credentials

# Hauptschleife für das Menü
$continue = $true
while ($continue) {
    $task = Show-Menu
    switch ($task) {
        "1" {
            $selection = ServerSelectionMenu
            if ($selection -eq "1") {
                $servers = Get-ServerList
                foreach ($server in $servers) {
                    $result = Search-WindowsUpdates -cred $credentials -ServerName $server
                    Write-Output $result
                }
            } elseif ($selection -eq "2") {
                $serverName = Read-Host "Gib den Servernamen ein"
                $result = Search-WindowsUpdates -cred $credentials -ServerName $serverName
                Write-Output $result
            }
        }
        "2" {
            $selection = ServerSelectionMenu
            if ($selection -eq "1") {
                $servers = Get-ServerList
                foreach ($server in $servers) {
                    $result = Install-WindowsUpdates -cred $credentials -ServerName $server
                    Write-Output $result
                }
            } elseif ($selection -eq "2") {
                $serverName = Read-Host "Gib den Servernamen ein"
                $result = Install-WindowsUpdates -cred $credentials -ServerName $serverName
                Write-Output $result
            }
        }
        "3" {
            $selection = ServerSelectionMenu
            if ($selection -eq "1") {
                $servers = Get-ServerList
                foreach ($server in $servers) {
                    $result = Get-WindowsUpdateStatus -cred $credentials -ServerName $server
                    Write-Output $result
                }
            } elseif ($selection -eq "2") {
                $serverName = Read-Host "Gib den Servernamen ein"
                $result = Get-WindowsUpdateStatus -cred $credentials -ServerName $serverName
                Write-Output $result
            }
        }
        "4" {
            $restartChoice = ServerSelectionMenu
            if ($restartChoice -eq "1") {
                Restart-AllServers -cred $credentials
            } elseif ($restartChoice -eq "2") {
                $serverName = Read-Host "Gib den Servernamen ein"
                Restart-Server -ServerName $serverName -cred $credentials
            }
        }
        "5" {
            $pingChoice = ServerSelectionMenu
            if ($pingChoice -eq "1") {
                $servers = Get-ServerList
                $results = @()
                foreach ($server in $servers) {
                    $result = Ping-Server -ServerName $server -cred $credentials
                    $results += $result
                }
                $results | Format-Table -AutoSize | Out-String | Write-Output
            } elseif ($pingChoice -eq "2") {
                $serverName = Read-Host "Gib den Servernamen ein"
                $result = Ping-Server -ServerName $serverName -cred $credentials
                $result | Format-Table -AutoSize | Out-String | Write-Output
            }
        }
        "6" {
            Write-Host "Skript beenden..." -ForegroundColor Red
            $continue = $false
        }
        default {
            Write-Host "Ungültige Auswahl, bitte erneut versuchen." -ForegroundColor Red
        }
    }

    # Abfrage, ob eine weitere Aufgabe durchgeführt werden soll
    if ($continue -and $task -ne "6") {
        $continueResponse = Read-Host "Möchtest du eine weitere Aufgabe durchführen? (j/n) [j]"
        if ($continueResponse -eq "n") {
            $continue = $false
        }
    }
}
