# Personen-<PERSON><PERSON>y definieren
$personen = @(
    @("<PERSON>", "a.kein<PERSON>", "+49 60519166960"),
    @("<PERSON>", "a.met<PERSON>ger", "+49 711 13265-41"),
    @("<PERSON><PERSON>", "b.supper", "+49 711 13265-56"),
    @("<PERSON><PERSON>", "d.ragger", "+49 711 13265-54"),
    @("<PERSON>", "e.uhlich", "+49 6081 405-190"),
    @("<PERSON>", "f.bleicher", "+49 711 13265-10"),
    @("<PERSON>", "f.heuser", "+49 711 13265-16"),
    @("<PERSON><PERSON>", "k.lue<PERSON><PERSON>", "+49 6081 405-151"),
    @("<PERSON>", "m.i<PERSON><PERSON>zi", "+49 711 13265-57"),
    @("<PERSON>", "m.mayer", "+49 711 13265-50"),
    @("<PERSON>", "m.fritz", "+49 711 13265-61"),
    @("<PERSON>", "m.fell<PERSON>", "+49 711 13265-41"),
    @("<PERSON>", "n.kutt<PERSON>", "+49 711 13265-84"),
    @("<PERSON> <PERSON>", "n.k<PERSON>", "+49 711 13265-62"),
    @("<PERSON> <PERSON><PERSON><PERSON><PERSON>", "p.th<PERSON><PERSON>", "+49 6081 405-122"),
    @("<PERSON> <PERSON><PERSON>ger", "r.s<PERSON><PERSON>", "+49 711 13265-82"),
    @("<PERSON>l<PERSON> <PERSON><PERSON>tt<PERSON>", "s.b<PERSON><PERSON><PERSON>", "+49 711 13265-22"),
    @("Tina Bleicher", "t.bleicher", "+49 711 13265-83"),
    @("Tomislav Brcko", "t.brcko", "+49 711 13265-59"),
    @("Tonino Gerns", "t.gerns", "+49 51193689123"),
    @("Verena Bleicher", "v.bleicher", "+49 711 13265-82"),
    @("Werner Bleicher", "w.bleicher", "+49 711 13265-0"),
    @("Wojciech Kutylo", "w.kutylo", "+49 711 13265-42")
)

# Funktion zum Finden des vollständigen Namens anhand des Benutzernamens
function Get-FullNameByUserName {
    param (
        [string]$username,
        [array]$personenArray
    )
    foreach ($person in $personenArray) {
        if ($person[1] -eq $username) {
            return $person[0]
        }
    }
    return $null
}

# Funktion zum Generieren der Abwesenheitsnachricht
function Set-AbwesenheitsNachricht {
    param (
        [string]$sickPerson,
        [datetime]$startdatum,
        [datetime]$enddatum,
        [string]$vertretungName,
        [string]$vertretungEmail,
        [string]$vertretungTelefon
    )

    $sickPersonFullname = Get-FullNameByUserName -username $sickPerson -personenArray $personen

    $nachricht = '$UserCredential = Get-Credential; '
    $nachricht += '$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://mail01.haug-components.com/PowerShell/ -Authentication Kerberos -Credential $UserCredential; '
    $nachricht += 'Import-PSSession $Session -DisableNameChecking; '
    $nachricht += "Set-MailboxAutoReplyConfiguration -Identity $sickPerson -AutoReplyState Scheduled "
    $nachricht += "-StartTime `"$($startdatum.ToString('MM/dd/yyyy')) 00:00:00`" "
    $nachricht += "-EndTime `"$($enddatum.ToString('MM/dd/yyyy')) 23:59:59`" "
    $nachricht += "-InternalMessage `"<html><body>Liebe Kolleginnen und Kollegen,<br><br>ich bin bis einschließlich $($enddatum.ToString('dd.MM.yyyy')) nicht im Büro<br>Bitte wenden Sie sich in meiner Abwesenheit an $vertretungName oder schicken eine Email an $vertretungEmail<br><br>Viele Grüße<br>$sickPersonFullname</body></html>`" "
    $nachricht += "-ExternalMessage `"<html><body>Sehr geehrte Damen und Herren,<br><br>ich bin bis einschließlich $($enddatum.ToString('dd.MM.yyyy')) nicht im Büro. / I will be out of office until $($enddatum.ToString('dd.MM.yyyy')) .<br>Ihre Email wird in dieser Zeit nicht weitergeleitet. <br><br>Bitte kontaktieren Sie in dringenden Fällen folgenden Ansprechpartner / Please contact in urgent matters the following contact person:<br>Email: $vertretungEmail<br>Tel./ Phone: $vertretungTelefon ($vertretungName)<br><br>Mit freundlichen Grüßen / Best Regards<br>$sickPersonFullname </body></html>`"; "
    $nachricht += 'Remove-PSSession $Session'

    return $nachricht
}

# Beispielaufruf
$sickPerson = "w.kutylo"
$startdatum = Get-Date "2024-11-18"
$enddatum = Get-Date "2024-11-22"
$vertretung = $personen | Where-Object { $_[1] -eq "m.fellermeier" }

$abwesenheitsNachricht = Set-AbwesenheitsNachricht -sickPerson $sickPerson `
                                                   -startdatum $startdatum `
                                                   -enddatum $enddatum `
                                                   -vertretungName $vertretung[0] `
                                                   -vertretungEmail "$($vertretung[1])@haug.solutions" `
                                                   -vertretungTelefon $vertretung[2]

# Ausgabe der generierten Nachricht
Write-Output $abwesenheitsNachricht

# Optional: Ausführen der generierten PowerShell-Befehle
# Invoke-Expression $abwesenheitsNachricht