$UserCredential = Get-Credential
$Session = New-PSSession -ConfigurationName Microsoft.Exchange -ConnectionUri http://mail01.haug-components.com/PowerShell/ -Authentication Kerberos -Credential $UserCredential
Import-PSSession $Session -DisableNameChecking

Function Manage-Delegates {
    param()

    $MailboxOwner = Read-Host "Bitte geben Sie den Benutzernamen der zu ändernden Mailbox ein"
    $Action = Read-Host "Möchten Sie Berechtigungen (H)inzufügen, (E)ntfernen, (Ä)ndern oder (A)nzeigen?"
    $Delegate = Read-Host "Bitte geben Sie den Benutzernamen des Stellvertreters ein, falls er<PERSON><PERSON><PERSON>, sonst einfach Enter drücken"
    $FolderPath = "$($MailboxOwner):\Posteingang"

    switch ($Action) {
        "H" {
            $AccessRights = Read-Host "Bitte wählen Sie die Zugriffsrechte: [1] <PERSON><PERSON>, [2] Bearbeiten (Standard)"
            $MailboxAccessRights = "FullAccess"
            switch ($AccessRights) {
                "1" { $FolderAccessRights = "Reviewer" }
                "2" { $FolderAccessRights = "Editor" }
            }
            Add-MailboxFolderPermission -Identity $FolderPath -User $Delegate -AccessRights $FolderAccessRights
            Add-MailboxPermission -Identity $MailboxOwner -User $Delegate -AccessRights $MailboxAccessRights -InheritanceType All
        }
        "E" {
            Remove-MailboxFolderPermission -Identity $FolderPath -User $Delegate -Confirm:$false
            Remove-MailboxPermission -Identity $MailboxOwner -User $Delegate -AccessRights FullAccess -InheritanceType All -Confirm:$false
        }
        "Ä" {
            $NewAccessRights = Read-Host "Bitte wählen Sie die neuen Zugriffsrechte: [1] Lesen, [2] Bearbeiten (Standard)"
            switch ($NewAccessRights) {
                "1" { $NewFolderAccessRights = "Reviewer" }
                "2" { $NewFolderAccessRights = "Editor" }
            }
            $ExistingPermission = Get-MailboxFolderPermission -Identity $FolderPath -User $Delegate -ErrorAction SilentlyContinue
            if ($null -eq $ExistingPermission) {
                Write-Host "Der Benutzer $Delegate hat derzeit keine Berechtigungen. Berechtigungen werden zunächst hinzugefügt."
                Add-MailboxFolderPermission -Identity $FolderPath -User $Delegate -AccessRights $NewFolderAccessRights
            }
            else {
                Set-MailboxFolderPermission -Identity $FolderPath -User $Delegate -AccessRights $NewFolderAccessRights
            }
            Set-MailboxPermission -Identity $MailboxOwner -User $Delegate -AccessRights FullAccess -InheritanceType All
        }
        "A" {
            # Nur Berechtigungen anzeigen
        }
    }

    # Berechtigungen anzeigen
    Write-Host "`nAktuelle Stellvertreterberechtigungen für den Posteingang:" -ForegroundColor Yellow
    $InboxDelegates = Get-MailboxFolderPermission -Identity ("$($MailboxOwner):\Posteingang") | Where-Object { $_.User -notlike "Default" -and $_.User -notlike "Anonymous" }
    foreach ($Delegate in $InboxDelegates) {
        Write-Host "  $($Delegate.User) - $($Delegate.AccessRights)"
    }
}

Manage-Delegates
